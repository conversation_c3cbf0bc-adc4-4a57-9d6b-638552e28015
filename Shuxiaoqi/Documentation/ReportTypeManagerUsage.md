# 举报类型管理器使用指南

## 概述

`ReportTypeManager` 是一个统一管理所有举报类型的管理器，提供了标准化的参数构建、验证和提交逻辑。

## 支持的举报类型

### 1. 个人信息举报 (type=0)
- **用途**: 举报用户的头像、昵称、签名等个人信息违规
- **特点**: 
  - 举报内容支持多选
  - 举报理由仅支持单选
  - 参数分别传递，不混合

### 2. 视频举报 (type=1) 
- **用途**: 举报用户发布的视频内容违规
- **特点**:
  - 支持多个视频同时举报
  - 支持上传图片证据
  - 支持文字描述

### 3. 评论举报 (type=2)
- **用途**: 举报视频下的评论内容违规
- **特点**:
  - 针对单条评论
  - 需要关联视频ID
  - 理由单选

### 4. 处罚相关 (type=3)
- **用途**: 后台前端专用，普通用户不可访问

## 使用方法

### 个人信息举报

```swift
// 构建举报数据
var builder = ReportTypeManager.PersonalInfoReportBuilder()
builder.reportedUserId = "user123"
builder.reportedUserName = "用户名"
builder.reportedUserAvatar = "头像URL"
builder.selectedContentValues = ["report_user_info_content_1", "report_user_info_content_2"] // 多选
builder.selectedReasonValue = "report_user_info_reason_3" // 单选

// 生成请求
let reportRequest = builder.build()

// 验证数据
let validation = ReportTypeManager.shared.validateReportRequest(reportRequest)
guard validation.isValid else {
    print("验证失败: \(validation.errorMessage)")
    return
}

// 打印调试信息
ReportTypeManager.shared.printDebugInfo(for: reportRequest)

// 提交举报
APIManager.shared.submitReport(reportData: reportRequest) { result in
    // 处理结果
}
```

### 视频举报

```swift
// 构建举报数据
var builder = ReportTypeManager.VideoReportBuilder()
builder.videoIds = ["123", "456"]
builder.reportedUserId = "user123"
builder.reportedUserName = "用户名"
builder.reportedUserAvatar = "头像URL"
builder.selectedReportValues = ["report_video_reason_1"]
builder.reportDescription = "举报描述"
builder.imageUrls = ["证据图片URL1", "证据图片URL2"]

// 生成请求
let reportRequest = builder.build()

// 验证和提交...
```

### 评论举报

```swift
// 构建举报数据
var builder = ReportTypeManager.CommentReportBuilder()
builder.commentId = "comment123"
builder.videoId = "video456"
builder.reportedUserId = "user123"
builder.reportedUserName = "用户名"
builder.reportedUserAvatar = "头像URL"
builder.selectedReasonValue = "report_comment_reason_1"

// 生成请求
let reportRequest = builder.build()

// 验证和提交...
```

## 数据验证

管理器提供了完整的数据验证功能：

```swift
let validation = ReportTypeManager.shared.validateReportRequest(reportRequest)
if validation.isValid {
    // 数据有效，可以提交
} else {
    // 数据无效，显示错误信息
    showError(validation.errorMessage)
}
```

## 调试信息

使用统一的调试信息输出：

```swift
ReportTypeManager.shared.printDebugInfo(for: reportRequest)
```

输出示例：
```
🔍 个人信息举报参数:
   dataType: 0
   type: 0
   status: 0
   reportCustomerId: user123
   reportUserInformationType (多选): report_user_info_content_1,report_user_info_content_2
   reportUserInformationReason (单选): report_user_info_reason_3
   reportCommentReason: 
```

## 获取可用举报类型

```swift
let availableTypes = ReportTypeManager.shared.getUserAccessibleReportTypes()
// 返回: [.personalInfo, .video, .comment] (不包含 .punishment)
```

## 优势

1. **统一管理**: 所有举报类型的逻辑集中管理
2. **参数标准化**: 避免参数传错或遗漏
3. **数据验证**: 提交前自动验证数据完整性
4. **调试友好**: 统一的调试信息输出格式
5. **类型安全**: 使用枚举避免硬编码类型值
6. **易于维护**: 新增举报类型只需扩展管理器

## 注意事项

1. 个人信息举报的内容和理由参数是分别传递的，不要混合
2. 评论举报必须包含视频ID
3. 视频举报支持多个视频ID，用逗号分隔
4. 处罚类型仅供后台使用，前端不应该使用
5. 所有举报的 dataType 都是 0（表示举报，1表示处罚）
