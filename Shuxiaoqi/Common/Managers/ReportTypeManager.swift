//
//  ReportTypeManager.swift
//  Shuxiaoqi
//
//  Created by AI Assistant on 2025/8/23.
//

import Foundation

/// 举报类型管理器
/// 统一管理所有举报类型的参数构建和提交逻辑
class ReportTypeManager {
    
    // MARK: - 单例
    static let shared = ReportTypeManager()
    private init() {}
    
    // MARK: - 举报类型枚举
    
    /// 举报类型
    enum ReportType: Int, CaseIterable {
        case personalInfo = 0   // 个人信息举报
        case video = 1          // 视频举报（稿件举报）
        case comment = 2        // 评论举报
        case punishment = 3     // 处罚相关（后台前端使用）
        
        var description: String {
            switch self {
            case .personalInfo:
                return "个人信息举报"
            case .video:
                return "视频举报"
            case .comment:
                return "评论举报"
            case .punishment:
                return "处罚相关"
            }
        }
        
        /// 是否为前端用户可用的举报类型
        var isUserAccessible: Bool {
            switch self {
            case .personalInfo, .video, .comment:
                return true
            case .punishment:
                return false // 后台前端专用
            }
        }
    }
    
    // MARK: - 举报数据构建器
    
    /// 个人信息举报数据构建器
    struct PersonalInfoReportBuilder {
        var reportedUserId: String = ""
        var reportedUserName: String = ""
        var reportedUserAvatar: String = ""
        var reportedUserSignature: String = ""
        var reportedUserImage: String = ""
        var selectedContentValues: [String] = [] // 多选内容
        var selectedReasonValue: String = ""     // 单选理由
        
        func build() -> ReportSubmitRequest {
            var request = ReportSubmitRequest()
            request.dataType = 0 // 举报
            request.type = ReportType.personalInfo.rawValue
            request.status = 0
            
            // 被举报用户信息
            request.reportCustomerId = reportedUserId
            request.reportUserInformationName = reportedUserName
            request.reportUserInformationAvatar = reportedUserAvatar
            request.reportUserInformationPersonalSignature = reportedUserSignature
            request.reportUserInformationImage = reportedUserImage
            
            // 举报内容和理由（分别传递，不混合）
            request.reportUserInformationType = selectedContentValues.joined(separator: ",")
            request.reportUserInformationReason = selectedReasonValue
            
            // 个人信息举报不使用 reportCommentReason 字段
            request.reportCommentReason = ""
            
            return request
        }
    }
    
    /// 视频举报数据构建器
    struct VideoReportBuilder {
        var videoIds: [String] = []
        var reportedUserId: String = ""
        var reportedUserName: String = ""
        var reportedUserAvatar: String = ""
        var selectedReportValues: [String] = []
        var reportDescription: String = ""
        var imageUrls: [String] = []
        
        func build() -> ReportSubmitRequest {
            var request = ReportSubmitRequest()
            request.dataType = 0 // 举报
            request.type = ReportType.video.rawValue
            request.status = 0
            
            // 视频信息
            request.reportVideoIds = videoIds.joined(separator: ",")
            
            // 被举报用户信息
            request.reportCustomerId = reportedUserId
            request.reportUserInformationName = reportedUserName
            request.reportUserInformationAvatar = reportedUserAvatar
            
            // 举报内容
            request.labelValues = selectedReportValues.joined(separator: ",")
            request.reportDescription = reportDescription
            request.description = reportDescription
            
            // 图片证据
            request.reportImages = imageUrls.joined(separator: ",")
            request.images = imageUrls.joined(separator: ",")
            
            return request
        }
    }
    
    /// 评论举报数据构建器
    struct CommentReportBuilder {
        var commentId: String = ""
        var videoId: String = ""
        var reportedUserId: String = ""
        var reportedUserName: String = ""
        var reportedUserAvatar: String = ""
        var selectedReasonValue: String = ""
        
        func build() -> ReportSubmitRequest {
            var request = ReportSubmitRequest()
            request.dataType = 0 // 举报
            request.type = ReportType.comment.rawValue
            request.status = 0
            
            // 评论信息
            request.reportCommentId = commentId
            request.reportVideoIds = videoId
            
            // 被举报用户信息
            request.reportCustomerId = reportedUserId
            request.reportUserInformationName = reportedUserName
            request.reportUserInformationAvatar = reportedUserAvatar
            
            // 评论举报理由
            request.reportCommentReason = selectedReasonValue
            request.labelValues = selectedReasonValue
            
            // 描述
            request.reportDescription = "评论举报"
            request.description = "评论举报"
            
            return request
        }
    }
    
    // MARK: - 公共方法
    
    /// 获取所有用户可访问的举报类型
    func getUserAccessibleReportTypes() -> [ReportType] {
        return ReportType.allCases.filter { $0.isUserAccessible }
    }
    
    /// 验证举报请求数据
    func validateReportRequest(_ request: ReportSubmitRequest) -> (isValid: Bool, errorMessage: String) {
        guard let reportType = ReportType(rawValue: request.type) else {
            return (false, "无效的举报类型")
        }
        
        switch reportType {
        case .personalInfo:
            return validatePersonalInfoReport(request)
        case .video:
            return validateVideoReport(request)
        case .comment:
            return validateCommentReport(request)
        case .punishment:
            return (false, "处罚类型不支持前端提交")
        }
    }
    
    // MARK: - 私有验证方法
    
    private func validatePersonalInfoReport(_ request: ReportSubmitRequest) -> (isValid: Bool, errorMessage: String) {
        if request.reportCustomerId.isEmpty {
            return (false, "被举报用户ID不能为空")
        }
        if request.reportUserInformationType.isEmpty {
            return (false, "请选择举报内容")
        }
        if request.reportUserInformationReason.isEmpty {
            return (false, "请选择举报理由")
        }
        return (true, "")
    }
    
    private func validateVideoReport(_ request: ReportSubmitRequest) -> (isValid: Bool, errorMessage: String) {
        if request.reportVideoIds.isEmpty {
            return (false, "举报视频ID不能为空")
        }
        if request.reportCustomerId.isEmpty {
            return (false, "被举报用户ID不能为空")
        }
        if request.labelValues.isEmpty {
            return (false, "请选择举报理由")
        }
        return (true, "")
    }
    
    private func validateCommentReport(_ request: ReportSubmitRequest) -> (isValid: Bool, errorMessage: String) {
        if request.reportCommentId.isEmpty {
            return (false, "举报评论ID不能为空")
        }
        if request.reportVideoIds.isEmpty {
            return (false, "视频ID不能为空")
        }
        if request.reportCustomerId.isEmpty {
            return (false, "被举报用户ID不能为空")
        }
        if request.reportCommentReason.isEmpty {
            return (false, "请选择举报理由")
        }
        return (true, "")
    }
    
    /// 打印举报请求调试信息
    func printDebugInfo(for request: ReportSubmitRequest) {
        guard let reportType = ReportType(rawValue: request.type) else {
            print("❌ 无效的举报类型: \(request.type)")
            return
        }
        
        print("🔍 \(reportType.description)参数:")
        print("   dataType: \(request.dataType)")
        print("   type: \(request.type)")
        print("   status: \(request.status)")
        
        switch reportType {
        case .personalInfo:
            print("   reportCustomerId: \(request.reportCustomerId)")
            print("   reportUserInformationType (多选): \(request.reportUserInformationType)")
            print("   reportUserInformationReason (单选): \(request.reportUserInformationReason)")
            print("   reportCommentReason: \(request.reportCommentReason)")
            
        case .video:
            print("   reportVideoIds: \(request.reportVideoIds)")
            print("   reportCustomerId: \(request.reportCustomerId)")
            print("   labelValues: \(request.labelValues)")
            print("   reportDescription: \(request.reportDescription)")
            print("   reportImages: \(request.reportImages)")
            
        case .comment:
            print("   reportCommentId: \(request.reportCommentId)")
            print("   reportVideoIds: \(request.reportVideoIds)")
            print("   reportCustomerId: \(request.reportCustomerId)")
            print("   reportCommentReason: \(request.reportCommentReason)")
            print("   labelValues: \(request.labelValues)")
            
        case .punishment:
            print("   处罚类型 - 后台专用")
        }
    }
}
