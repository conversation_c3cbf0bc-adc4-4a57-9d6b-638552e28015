//
//  VideoReportViewController.swift
//  Shu<PERSON><PERSON>qi
//
//  Created by AI Assistant on 2025/8/20.
//

import UIKit
import SnapKit
import HXPhotoPicker

/// 视频举报页面
class VideoReportViewController: BaseViewController {

    // MARK: - 属性

    /// 举报页面数据
    private var reportData = ReportPageData()

    /// 举报字典分类数据
    private var reportCategories: [ReportDictCategory] = []

    /// 选中的举报类型按钮（改为单选）
    private var selectedReportButton: UIButton?

    /// 选中的图片数组
    private var selectedImages: [UIImage] = [] {
        didSet {
            imageCollectionView.reloadData()
            updateSubmitButtonState()
        }
    }

    /// 举报类型：true=个人作品举报(type=1)，false=视频举报(type=2)
    private var isPersonalWorksReport: Bool = false

    /// 最大图片数量
    private let maxImageCount = 5

    /// 选中的视频列表（用于多视频举报）
    private var selectedVideoItems: [VideoItem] = []

    // MARK: - UI组件
    
    /// 主滚动视图
    private let scrollView = UIScrollView()
    private let scrollContentView = UIView()

    /// 多视频展示容器
    private var multiVideoContainer = UIView()

    /// 举报类型容器
    private let reportTypesContainer = UIView()
    
    /// 举报描述输入框
    private let descriptionTextView = UITextView()
    private let descriptionPlaceholderLabel = UILabel()
    private let descriptionCountLabel = UILabel()
    
    /// 图片上传容器
    private let imageUploadContainer = UIView()

    /// 图片横向滚动视图
    private lazy var imageCollectionView: UICollectionView = {
        let layout = UICollectionViewFlowLayout()
        layout.scrollDirection = .horizontal
        layout.itemSize = CGSize(width: 90, height: 90) // 增加Cell尺寸为删除按钮预留空间
        layout.minimumInteritemSpacing = 8
        layout.minimumLineSpacing = 8
        let cv = UICollectionView(frame: .zero, collectionViewLayout: layout)
        cv.backgroundColor = .clear
        cv.showsHorizontalScrollIndicator = false
        cv.isScrollEnabled = true
        cv.register(ReportImageCell.self, forCellWithReuseIdentifier: "ReportImageCell")
        cv.register(ReportAddImageCell.self, forCellWithReuseIdentifier: "ReportAddImageCell")
        cv.dataSource = self
        cv.delegate = self
        return cv
    }()
    
    /// 提交按钮容器（添加白色背景）
    private let submitButtonContainer = UIView()
    
    /// 提交按钮
    private let submitButton = UIButton(type: .custom)
    
    // MARK: - 初始化
    
    /// 初始化举报页面
    /// - Parameters:
    ///   - videoId: 被举报的视频ID（单个视频）
    ///   - reportedUserId: 被举报的用户ID
    ///   - reportedUserName: 被举报的用户名
    ///   - reportedUserAvatar: 被举报的用户头像
    init(videoId: String, reportedUserId: String = "", reportedUserName: String = "", reportedUserAvatar: String = "") {
        super.init(nibName: nil, bundle: nil)

        reportData.videoId = videoId
        reportData.reportedUserId = reportedUserId
        reportData.reportedUserName = reportedUserName
        reportData.reportedUserAvatar = reportedUserAvatar
    }

    /// 初始化举报页面（多个视频）
    /// - Parameters:
    ///   - videoItems: 被举报的视频列表
    ///   - reportedUserId: 被举报的用户ID
    ///   - reportedUserName: 被举报的用户名
    ///   - reportedUserAvatar: 被举报的用户头像
    ///   - isPersonalWorksReport: 是否为个人作品举报，true=个人作品举报(type=1)，false=视频举报(type=2)
    init(videoItems: [VideoItem], reportedUserId: String = "", reportedUserName: String = "", reportedUserAvatar: String = "", isPersonalWorksReport: Bool = false) {
        super.init(nibName: nil, bundle: nil)

        self.selectedVideoItems = videoItems
        self.isPersonalWorksReport = isPersonalWorksReport
        let videoIds = videoItems.compactMap { $0.id }.map { String($0) }
        reportData.videoId = videoIds.joined(separator: ",")
        reportData.reportedUserId = reportedUserId
        reportData.reportedUserName = reportedUserName
        reportData.reportedUserAvatar = reportedUserAvatar
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - 生命周期
    
    override func viewDidLoad() {
        super.viewDidLoad()
        
        setupUI()
        loadReportDict()
    }
    
    // MARK: - UI设置
    
    private func setupUI() {
        // 设置导航栏
        navTitle = "举报理由"
        showBackButton = true

        // 设置背景色为白色
        contentView.backgroundColor = .white
        view.backgroundColor = .white
        
        // 添加滚动视图
        contentView.addSubview(scrollView)
        scrollView.addSubview(scrollContentView)

        scrollView.snp.makeConstraints { make in
            make.top.left.right.equalToSuperview()
            make.bottom.equalToSuperview() // 初始约束，后续会在setupConstraints中更新
        }

        scrollContentView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.width.equalToSuperview()
        }

        setupMultiVideoContainer()
        setupReportTypesContainer()
        setupDescriptionSection()
        setupImageUploadSection()
        setupSubmitButton()
        setupConstraints()
    }

    private func setupMultiVideoContainer() {
        // 只要有视频就显示容器（包括只有1个视频的情况）
        guard !selectedVideoItems.isEmpty else { return }

        scrollContentView.addSubview(multiVideoContainer)
        multiVideoContainer.backgroundColor = .white
        multiVideoContainer.layer.cornerRadius = 8
        multiVideoContainer.layer.shadowColor = UIColor(red: 0, green: 0, blue: 0, alpha: 0.1).cgColor
        multiVideoContainer.layer.shadowOffset = CGSize(width: 0, height: 2)
        multiVideoContainer.layer.shadowOpacity = 1
        multiVideoContainer.layer.shadowRadius = 4

        // 标题
        let titleLabel = UILabel()
        titleLabel.text = "已选\(selectedVideoItems.count)个作品"
        titleLabel.font = .systemFont(ofSize: 16, weight: .medium)
        titleLabel.textColor = UIColor(hex: "#333333")
        multiVideoContainer.addSubview(titleLabel)

        titleLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(16)
            make.left.equalToSuperview().offset(16)
        }

        // 视频列表
        var lastView: UIView = titleLabel
        for (index, videoItem) in selectedVideoItems.enumerated() {
            let videoRowView = createVideoRowView(videoItem: videoItem, index: index)
            multiVideoContainer.addSubview(videoRowView)

            videoRowView.snp.makeConstraints { make in
                make.top.equalTo(lastView.snp.bottom).offset(12)
                make.left.equalToSuperview().offset(16)
                make.right.equalToSuperview().offset(-16)
                make.height.equalTo(44)
            }

            lastView = videoRowView
        }

        // 设置容器底部约束
        lastView.snp.makeConstraints { make in
            make.bottom.equalToSuperview().offset(-16)
        }
    }

    private func createVideoRowView(videoItem: VideoItem, index: Int) -> UIView {
        let containerView = UIView()
        containerView.backgroundColor = UIColor(hex: "#F8F8F8")
        containerView.layer.cornerRadius = 4

        // 视频标题
        let titleLabel = UILabel()
        titleLabel.text = videoItem.worksTitle ?? "无标题"
        titleLabel.font = .systemFont(ofSize: 14)
        titleLabel.textColor = UIColor(hex: "#333333")
        titleLabel.numberOfLines = 1
        containerView.addSubview(titleLabel)

        // 清除按钮
        let clearButton = UIButton(type: .custom)
        clearButton.setImage(UIImage(named: "reporting_module_clear"), for: .normal)
        clearButton.tag = index
        clearButton.addTarget(self, action: #selector(clearVideoButtonTapped(_:)), for: .touchUpInside)
        containerView.addSubview(clearButton)

        titleLabel.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(12)
            make.centerY.equalToSuperview()
            make.right.equalTo(clearButton.snp.left).offset(-8)
        }

        clearButton.snp.makeConstraints { make in
            make.right.equalToSuperview().offset(-12)
            make.centerY.equalToSuperview()
            make.width.height.equalTo(20)
        }

        return containerView
    }

    @objc private func clearVideoButtonTapped(_ sender: UIButton) {
        let index = sender.tag
        guard index < selectedVideoItems.count else { return }

        // 获取要移除的视频信息，用于同步更新个人举报页
        let removedVideoItem = selectedVideoItems[index]
        
        // 移除视频
        selectedVideoItems.remove(at: index)

        // 同步更新个人举报页的选择状态
        syncVideoSelectionToPersonalReportPage(removedVideoItem: removedVideoItem)

        // 更新reportData中的videoId
        let videoIds = selectedVideoItems.compactMap { $0.id }.map { String($0) }
        reportData.videoId = videoIds.joined(separator: ",")

        // 如果没有视频了，返回上一页
        if selectedVideoItems.isEmpty {
            navigationController?.popViewController(animated: true)
            return
        }

        // 重新构建多视频容器以更新所有index和tag
        rebuildMultiVideoContainer()
    }

    private func setupReportTypesContainer() {
        scrollContentView.addSubview(reportTypesContainer)
        reportTypesContainer.backgroundColor = .white
        
        // 添加提示文本
        let tipLabel = UILabel()
        tipLabel.text = "请选择符合的举报原因，帮助我们进行处理"
        tipLabel.font = UIFont.systemFont(ofSize: 14)
        tipLabel.textColor = UIColor(hex: "#666666")
        tipLabel.numberOfLines = 0
        reportTypesContainer.addSubview(tipLabel)
        
        tipLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(16)
            make.left.equalToSuperview().offset(16)
            make.right.equalToSuperview().offset(-16)
        }
    }
    
    private func setupDescriptionSection() {
        // 描述输入区域
        let descriptionContainer = UIView()
        descriptionContainer.backgroundColor = .white
        // 添加阴影效果
        descriptionContainer.layer.shadowColor = UIColor(red: 0, green: 0, blue: 0, alpha: 0.15).cgColor
        descriptionContainer.layer.shadowOffset = CGSize(width: 0, height: 2)
        descriptionContainer.layer.shadowOpacity = 1
        descriptionContainer.layer.shadowRadius = 4
        descriptionContainer.layer.cornerRadius = 8
        scrollContentView.addSubview(descriptionContainer)
        
        // 标题
        let titleLabel = UILabel()
        titleLabel.text = "举报描述（必填）"
        titleLabel.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        titleLabel.textColor = UIColor(hex: "#333333")
        descriptionContainer.addSubview(titleLabel)
        
        // 输入框 - 白色背景，无边框
        descriptionTextView.backgroundColor = .white
        descriptionTextView.layer.cornerRadius = 8
        descriptionTextView.font = UIFont.systemFont(ofSize: 14)
        descriptionTextView.textColor = UIColor(hex: "#333333")
        descriptionTextView.delegate = self
        descriptionContainer.addSubview(descriptionTextView)
        
        // 占位符
        descriptionPlaceholderLabel.text = "请详细描述号，以便我们更好的处理"
        descriptionPlaceholderLabel.font = UIFont.systemFont(ofSize: 14)
        descriptionPlaceholderLabel.textColor = UIColor(hex: "#999999")
        descriptionTextView.addSubview(descriptionPlaceholderLabel)
        
        // 字数统计
        descriptionCountLabel.text = "0/100"
        descriptionCountLabel.font = UIFont.systemFont(ofSize: 12)
        descriptionCountLabel.textColor = UIColor(hex: "#999999")
        descriptionCountLabel.textAlignment = .right
        descriptionContainer.addSubview(descriptionCountLabel)
        
        // 约束
        titleLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(16)
            make.left.equalToSuperview().offset(16)
            make.right.equalToSuperview().offset(-16)
        }
        
        descriptionTextView.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(12)
            make.left.equalToSuperview().offset(16)
            make.right.equalToSuperview().offset(-16)
            make.height.equalTo(100)
        }
        
        descriptionPlaceholderLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(8)
            make.left.equalToSuperview().offset(8)
        }
        
        descriptionCountLabel.snp.makeConstraints { make in
            make.top.equalTo(descriptionTextView.snp.bottom).offset(8)
            make.right.equalToSuperview().offset(-16)
            make.bottom.equalToSuperview().offset(-16)
        }
        
        self.descriptionContainer = descriptionContainer
    }
    
    private func setupImageUploadSection() {
        // 图片上传区域
        imageUploadContainer.backgroundColor = .white
        // 添加阴影效果
        imageUploadContainer.layer.shadowColor = UIColor(red: 0, green: 0, blue: 0, alpha: 0.15).cgColor
        imageUploadContainer.layer.shadowOffset = CGSize(width: 0, height: 2)
        imageUploadContainer.layer.shadowOpacity = 1
        imageUploadContainer.layer.shadowRadius = 4
        imageUploadContainer.layer.cornerRadius = 8
        scrollContentView.addSubview(imageUploadContainer)
        
        // 标题
        let titleLabel = UILabel()
        titleLabel.text = "图片上传"
        titleLabel.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        titleLabel.textColor = UIColor(hex: "#333333")
        imageUploadContainer.addSubview(titleLabel)
        
        // 提示文本
        let tipLabel = UILabel()
        tipLabel.text = "最多上传5张，最大5M"
        tipLabel.font = UIFont.systemFont(ofSize: 12)
        tipLabel.textColor = UIColor(hex: "#999999")
        imageUploadContainer.addSubview(tipLabel)

        // 图片横向滚动视图
        imageUploadContainer.addSubview(imageCollectionView)

        // 约束
        titleLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(16)
            make.left.equalToSuperview().offset(16)
        }

        tipLabel.snp.makeConstraints { make in
            make.centerY.equalTo(titleLabel)
            make.right.equalToSuperview().offset(-16)
        }

        imageCollectionView.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(12)
            make.left.equalToSuperview().offset(16)
            make.right.equalToSuperview().offset(-16)
            make.height.equalTo(90) // 增加高度为删除按钮预留空间
            make.bottom.equalToSuperview().offset(-16)
        }
    }
    
    private func setupSubmitButton() {
        // 设置提交按钮容器
        submitButtonContainer.backgroundColor = .white
        // 添加上边框阴影
        submitButtonContainer.layer.shadowColor = UIColor.black.withAlphaComponent(0.1).cgColor
        submitButtonContainer.layer.shadowOffset = CGSize(width: 0, height: -2)
        submitButtonContainer.layer.shadowOpacity = 1
        submitButtonContainer.layer.shadowRadius = 4
        contentView.addSubview(submitButtonContainer)
        
        submitButton.backgroundColor = UIColor(hex: "#E5E5E5") // 初始状态为灰色
        submitButton.setTitle("提交", for: .normal)
        submitButton.setTitleColor(.white, for: .normal)
        submitButton.titleLabel?.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        submitButton.layer.cornerRadius = 6
        submitButton.isEnabled = false // 初始状态不可交互
        submitButton.addTarget(self, action: #selector(submitButtonTapped), for: .touchUpInside)
        // 提交按钮添加到容器中
        submitButtonContainer.addSubview(submitButton)
    }
    
    private func setupConstraints() {
        // 清除之前的约束
        reportTypesContainer.snp.removeConstraints()
        descriptionContainer.snp.removeConstraints()
        imageUploadContainer.snp.removeConstraints()

        let topView: UIView
        if !selectedVideoItems.isEmpty {
            // 有视频时（包括只有1个视频）
            multiVideoContainer.snp.makeConstraints { make in
                make.top.equalToSuperview().offset(16)
                make.left.right.equalToSuperview().inset(16)
            }
            topView = multiVideoContainer
        } else {
            topView = scrollContentView
        }

        reportTypesContainer.snp.makeConstraints { make in
            if !selectedVideoItems.isEmpty {
                make.top.equalTo(multiVideoContainer.snp.bottom).offset(16)
            } else {
                make.top.equalToSuperview().offset(16)
            }
            make.left.right.equalToSuperview() // 移除容器的左右边距，让内部元素直接控制边距
        }

        descriptionContainer.snp.makeConstraints { make in
            make.top.equalTo(reportTypesContainer.snp.bottom).offset(16)
            make.left.right.equalToSuperview().inset(16)
        }
        
        // imageUploadContainer约束已在setupConstraints中设置
        
        // 设置提交按钮容器约束
        submitButtonContainer.snp.makeConstraints { make in
            make.left.right.equalToSuperview()
            if #available(iOS 11.0, *) {
                make.bottom.equalTo(view.safeAreaLayoutGuide.snp.bottom)
            } else {
                make.bottom.equalToSuperview()
            }
            make.height.equalTo(75) // 容器高度：按钮43 + 上下边距16*2 = 75
        }
        
        // 设置提交按钮约束
        submitButton.snp.makeConstraints { make in
            make.left.right.equalToSuperview().inset(16)
            make.top.equalToSuperview().offset(16)
            make.height.equalTo(43)
        }

        // 重新设置滚动视图约束，避免被提交按钮遮挡
        scrollView.snp.remakeConstraints { make in
            make.top.left.right.equalToSuperview()
            make.bottom.equalTo(submitButtonContainer.snp.top)
        }

        // 更新滚动内容的底部约束
        imageUploadContainer.snp.makeConstraints { make in
            make.top.equalTo(descriptionContainer.snp.bottom).offset(16)
            make.left.right.equalToSuperview().inset(16)
            make.bottom.equalToSuperview().offset(-16)
        }
    }
    
    // MARK: - 私有属性
    private var descriptionContainer: UIView!
    
    /// 重新构建多视频容器（用于解决删除视频后的index错乱问题）
    private func rebuildMultiVideoContainer() {
        // 移除旧的容器
        multiVideoContainer.removeFromSuperview()
        
        // 重新初始化容器对象
        multiVideoContainer = UIView()
        
        // 重新创建容器
        setupMultiVideoContainer()
        
        // 只重新设置多视频容器相关的约束
        setupMultiVideoContainerConstraints()
    }
    
    /// 设置多视频容器相关约束
    private func setupMultiVideoContainerConstraints() {
        // 清除之前的约束
        reportTypesContainer.snp.removeConstraints()
        descriptionContainer.snp.removeConstraints()
        imageUploadContainer.snp.removeConstraints()
        
        // 设置多视频容器约束
        if !selectedVideoItems.isEmpty {
            multiVideoContainer.snp.makeConstraints { make in
                make.top.equalToSuperview().offset(16)
                make.left.right.equalToSuperview().inset(16)
            }
        }
        
        // 设置其他容器的约束
        reportTypesContainer.snp.makeConstraints { make in
            if !selectedVideoItems.isEmpty {
                make.top.equalTo(multiVideoContainer.snp.bottom).offset(16)
            } else {
                make.top.equalToSuperview().offset(16)
            }
            make.left.right.equalToSuperview()
        }
        
        descriptionContainer.snp.makeConstraints { make in
            make.top.equalTo(reportTypesContainer.snp.bottom).offset(16)
            make.left.right.equalToSuperview().inset(16)
        }
        
        imageUploadContainer.snp.makeConstraints { make in
            make.top.equalTo(descriptionContainer.snp.bottom).offset(16)
            make.left.right.equalToSuperview().inset(16)
            make.bottom.equalToSuperview().offset(-16)
        }
    }
    
    /// 同步更新个人举报页的视频选择状态
    private func syncVideoSelectionToPersonalReportPage(removedVideoItem: VideoItem) {
        // 遍历导航堆栈，找到PersonalReportViewController
        guard let navigationController = navigationController else { return }
        
        for viewController in navigationController.viewControllers {
            if let personalReportVC = viewController as? PersonalReportViewController {
                // 调用公开方法移除视频选择
                personalReportVC.removeVideoSelection(videoItem: removedVideoItem)
                break
            }
        }
    }

    // MARK: - 数据加载

    private func loadReportDict() {
        // 显示加载状态
        showLoadingIndicator()

        APIManager.shared.getReportDict { [weak self] result in
            DispatchQueue.main.async {
                self?.hideLoadingIndicator()

                switch result {
                case .success(let response):
                    if response.isSuccess {
                        self?.reportCategories = response.data
                        self?.setupReportTypeButtons()
                    } else {
                        self?.showErrorAlert(message: response.displayMessage)
                    }
                case .failure(let error):
                    self?.showErrorAlert(message: error.errorMessage)
                }
            }
        }
    }

    private func setupReportTypeButtons() {
        // 清除现有按钮
        reportTypesContainer.subviews.forEach { view in
            if view is UIButton {
                view.removeFromSuperview()
            }
        }

        var lastView: UIView? = nil

        // 为每个分类创建按钮组
        for category in reportCategories {
            // 分类标题
            let categoryLabel = UILabel()
            categoryLabel.text = category.description
            categoryLabel.font = UIFont.systemFont(ofSize: 16, weight: .medium)
            categoryLabel.textColor = UIColor(hex: "#333333")
            reportTypesContainer.addSubview(categoryLabel)

            categoryLabel.snp.makeConstraints { make in
                if let lastView = lastView {
                    make.top.equalTo(lastView.snp.bottom).offset(24)
                } else {
                    make.top.equalToSuperview().offset(60) // 在提示文本下方
                }
                make.left.equalToSuperview().offset(16)
                make.right.equalToSuperview().offset(-16)
            }

            lastView = categoryLabel

            // 创建该分类下的选项按钮
            let buttonsContainer = createReportTypeButtons(for: category.dictDetails)
            reportTypesContainer.addSubview(buttonsContainer)

            buttonsContainer.snp.makeConstraints { make in
                make.top.equalTo(categoryLabel.snp.bottom).offset(12)
                make.left.equalToSuperview().offset(16)
                make.right.equalToSuperview().offset(-16)
            }

            lastView = buttonsContainer
        }

        // 更新容器高度约束
        if let lastView = lastView {
            lastView.snp.makeConstraints { make in
                make.bottom.equalToSuperview().offset(-16)
            }
        }
    }

    private func createReportTypeButtons(for details: [ReportDictDetail]) -> UIView {
        let container = UIView()

        let buttonHeight: CGFloat = 30
        let horizontalSpacing: CGFloat = 12
        let verticalSpacing: CGFloat = 12
        let leftMargin: CGFloat = 16
        let rightMargin: CGFloat = 16
        let containerWidth = UIScreen.main.bounds.width - leftMargin - rightMargin

        var currentX: CGFloat = 0
        var currentY: CGFloat = 0

        for detail in details {
            let button = UIButton(type: .custom)
            button.setTitle(detail.label, for: .normal)
            button.setTitleColor(UIColor(hex: "#333333"), for: .normal)
            button.setTitleColor(UIColor(hex: "#FF8F1F"), for: .selected)
            button.titleLabel?.font = UIFont.systemFont(ofSize: 14)
            button.backgroundColor = UIColor(hex: "#F5F5F5")
            button.layer.cornerRadius = 8
            button.layer.borderWidth = 0 // 默认无边框

            // 计算按钮宽度：文字宽度 + 24pt（左右各12pt）
            let textWidth = detail.label.size(withAttributes: [.font: UIFont.systemFont(ofSize: 14)]).width
            let buttonWidth = textWidth + 24

            // 检查当前行是否能放下这个按钮
            if currentX + buttonWidth > containerWidth && currentX > 0 {
                // 换行
                currentX = 0
                currentY += buttonHeight + verticalSpacing
            }

            // 存储举报类型值
            button.accessibilityIdentifier = detail.value

            button.addTarget(self, action: #selector(reportTypeButtonTapped(_:)), for: .touchUpInside)
            container.addSubview(button)

            button.snp.makeConstraints { make in
                make.left.equalToSuperview().offset(currentX)
                make.top.equalToSuperview().offset(currentY)
                make.width.equalTo(buttonWidth)
                make.height.equalTo(buttonHeight)
            }

            currentX += buttonWidth + horizontalSpacing
        }

        // 设置容器高度
        let containerHeight = currentY + buttonHeight

        container.snp.makeConstraints { make in
            make.height.equalTo(containerHeight)
        }

        return container
    }

    // MARK: - 事件处理

    @objc private func reportTypeButtonTapped(_ sender: UIButton) {
        // 单选逻辑：先取消之前选中的按钮
        if let previousButton = selectedReportButton, previousButton != sender {
            previousButton.isSelected = false
            previousButton.backgroundColor = UIColor(hex: "#F5F5F5")
            previousButton.layer.borderWidth = 0
        }
        
        // 切换当前按钮状态
        sender.isSelected = !sender.isSelected
        
        if sender.isSelected {
            // 选中状态：白色背景，橙色边框和文字
            sender.backgroundColor = .white
            sender.layer.borderWidth = 1
            sender.layer.borderColor = UIColor(hex: "#FF8F1F").cgColor
            selectedReportButton = sender
            
            // 设置选中的举报类型值（单个值）
            if let value = sender.accessibilityIdentifier {
                reportData.selectedReportValues = [value]
            }
        } else {
            // 取消选中状态：灰色背景，无边框
            sender.backgroundColor = UIColor(hex: "#F5F5F5")
            sender.layer.borderWidth = 0
            selectedReportButton = nil
            
            // 清空选中的举报类型值
            reportData.selectedReportValues = []
        }
        
        updateSubmitButtonState()
    }

    @objc private func addImageButtonTapped() {
        openImagePicker()
    }

    @objc private func submitButtonTapped() {
        // 验证输入
        guard !reportData.selectedReportValues.isEmpty else {
            showToast("请选择举报理由")
            return
        }

        guard !reportData.reportDescription.isEmpty else {
            showToast("请填写举报描述")
            return
        }

        // 如果有图片，先上传图片
        if !selectedImages.isEmpty {
            showLoadingIndicator()
            uploadImages(selectedImages) { [weak self] imageUrls in
                DispatchQueue.main.async {
                    guard let self = self else { return }
                    if imageUrls.isEmpty {
                        self.hideLoadingIndicator()
                        self.showErrorAlert(message: "图片上传失败，请重试")
                        return
                    }
                    // 图片上传成功，提交举报
                    self.submitReportWithImages(imageUrls)
                }
            }
        } else {
            // 没有图片，直接提交举报
            submitReportWithImages([])
        }
    }

    private func submitReportWithImages(_ imageUrls: [String]) {
        let reportRequest: ReportSubmitRequest

        if isPersonalWorksReport {
            // 个人作品举报 (type=1)
            var builder = ReportTypeManager.PersonalWorksReportBuilder()
            builder.videoIds = reportData.videoId.components(separatedBy: ",")
            builder.reportedUserId = reportData.reportedUserId
            builder.reportedUserName = reportData.reportedUserName
            builder.reportedUserAvatar = reportData.reportedUserAvatar
            builder.selectedReportValues = reportData.selectedReportValues
            builder.reportDescription = reportData.reportDescription
            builder.imageUrls = imageUrls
            reportRequest = builder.build()
        } else {
            // 视频举报 (type=2)
            var builder = ReportTypeManager.VideoReportBuilder()
            builder.videoIds = reportData.videoId.components(separatedBy: ",")
            builder.reportedUserId = reportData.reportedUserId
            builder.reportedUserName = reportData.reportedUserName
            builder.reportedUserAvatar = reportData.reportedUserAvatar
            builder.selectedReportValues = reportData.selectedReportValues
            builder.reportDescription = reportData.reportDescription
            builder.imageUrls = imageUrls
            reportRequest = builder.build()
        }

        // 验证请求数据
        let validation = ReportTypeManager.shared.validateReportRequest(reportRequest)
        guard validation.isValid else {
            showErrorAlert(message: validation.errorMessage)
            return
        }

        // 打印调试信息
        let reportTypeName = isPersonalWorksReport ? "个人作品举报" : "视频举报"
        print("🔍 \(reportTypeName)参数:")
        ReportTypeManager.shared.printDebugInfo(for: reportRequest)

        // 提交举报
        if selectedImages.isEmpty {
            showLoadingIndicator()
        }

        APIManager.shared.submitReport(reportData: reportRequest) { [weak self] result in
            DispatchQueue.main.async {
                self?.hideLoadingIndicator()

                switch result {
                case .success(let response):
                    if response.isSuccess {
                        self?.showSuccessAlert()
                    } else {
                        self?.showErrorAlert(message: response.displayMessage)
                    }
                case .failure(let error):
                    self?.showErrorAlert(message: error.errorMessage)
                }
            }
        }
    }

    private func updateSubmitButtonState() {
        let hasSelectedReports = !reportData.selectedReportValues.isEmpty
        let hasDescription = !reportData.reportDescription.isEmpty

        submitButton.isEnabled = hasSelectedReports && hasDescription

        if submitButton.isEnabled {
            submitButton.backgroundColor = UIColor(hex: "#FF8F1F")
        } else {
            submitButton.backgroundColor = UIColor(hex: "#E5E5E5")
        }
    }

    // MARK: - 图片上传

    /// 上传图片到七牛云，返回图片URL数组
    private func uploadImages(_ images: [UIImage], completion: @escaping ([String]) -> Void) {
        print("[举报] 开始上传图片，共\(images.count)张")
        
        // 显示上传Loading HUD
        showUploadingHUD()
        
        let imageDatas = images.compactMap { $0.jpegData(compressionQuality: 0.9) }
        guard !imageDatas.isEmpty else {
            print("[举报] 图片数据为空")
            hideUploadingHUD()
            completion([])
            return
        }

        APIManager.shared.uploadFileQNRaw(files: imageDatas) { [weak self] result in
            DispatchQueue.main.async {
                self?.hideUploadingHUD()
                
                switch result {
                case .success(let response):
                    print("[举报] 上传成功: \(response)")
                    let urls = response.data?.flatMap { $0.data } ?? []
                    completion(urls)
                case .failure(let error):
                    print("[举报] 上传失败: \(error)")
                    completion([])
                }
            }
        }
    }

    // MARK: - 辅助方法

    private func showLoadingIndicator() {
        // 这里可以显示加载指示器
        // 可以使用项目中现有的加载指示器组件
    }

    private func hideLoadingIndicator() {
        // 隐藏加载指示器
    }

    private func showErrorAlert(message: String) {
        let alert = UIAlertController(title: "提示", message: message, preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "确定", style: .default))
        present(alert, animated: true)
    }

    private func showSuccessAlert() {
        // 使用HUD显示成功提示
        showSuccessHUD(message: "提交成功，请等待审核")

        // 2秒后自动dismiss页面
        DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) { [weak self] in
            self?.dismissReportPage()
        }
    }

    private func showSuccessHUD(message: String) {
        // 创建HUD视图
        let hudView = UIView()
        hudView.backgroundColor = UIColor.black.withAlphaComponent(0.8)
        hudView.layer.cornerRadius = 8
        hudView.tag = 9999 // 用于后续移除

        // 添加文字标签
        let messageLabel = UILabel()
        messageLabel.text = message
        messageLabel.textColor = .white
        messageLabel.font = UIFont.systemFont(ofSize: 16)
        messageLabel.textAlignment = .center
        messageLabel.numberOfLines = 0
        hudView.addSubview(messageLabel)

        // 添加到主视图
        view.addSubview(hudView)

        // 设置约束
        messageLabel.snp.makeConstraints { make in
            make.edges.equalToSuperview().inset(16)
        }

        hudView.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.width.greaterThanOrEqualTo(200)
            make.height.greaterThanOrEqualTo(60)
        }

        // 显示动画
        hudView.alpha = 0
        hudView.transform = CGAffineTransform(scaleX: 0.8, y: 0.8)
        UIView.animate(withDuration: 0.3) {
            hudView.alpha = 1
            hudView.transform = .identity
        }
    }
    
    private func showUploadingHUD() {
        // 创建上传Loading HUD
        let hudView = UIView()
        hudView.backgroundColor = UIColor.black.withAlphaComponent(0.8)
        hudView.layer.cornerRadius = 8
        hudView.tag = 8888 // 用于标识上传HUD
        
        // 添加活动指示器
        let activityIndicator = UIActivityIndicatorView(style: .whiteLarge)
        activityIndicator.startAnimating()
        hudView.addSubview(activityIndicator)
        
        // 添加文字标签
        let messageLabel = UILabel()
        messageLabel.text = "上传中..."
        messageLabel.textColor = .white
        messageLabel.font = UIFont.systemFont(ofSize: 16)
        messageLabel.textAlignment = .center
        hudView.addSubview(messageLabel)
        
        // 添加到主视图
        view.addSubview(hudView)
        
        // 设置约束
        activityIndicator.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.top.equalToSuperview().offset(20)
        }
        
        messageLabel.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.top.equalTo(activityIndicator.snp.bottom).offset(12)
            make.left.right.equalToSuperview().inset(16)
            make.bottom.equalToSuperview().offset(-20)
        }
        
        hudView.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.width.greaterThanOrEqualTo(120)
            make.height.greaterThanOrEqualTo(100)
        }
        
        // 显示动画
        hudView.alpha = 0
        hudView.transform = CGAffineTransform(scaleX: 0.8, y: 0.8)
        UIView.animate(withDuration: 0.3) {
            hudView.alpha = 1
            hudView.transform = .identity
        }
    }
    
    private func hideUploadingHUD() {
        // 移除上传HUD
        if let hudView = view.subviews.first(where: { $0.tag == 8888 }) {
            UIView.animate(withDuration: 0.3, animations: {
                hudView.alpha = 0
                hudView.transform = CGAffineTransform(scaleX: 0.8, y: 0.8)
            }) { _ in
                hudView.removeFromSuperview()
            }
        }
    }

    private func dismissReportPage() {
        // 移除HUD
        view.subviews.first { $0.tag == 9999 }?.removeFromSuperview()

        // 根据展示方式选择合适的dismiss方法
        if let navigationController = navigationController {
            // 检查是否是从个人举报页面进入的（多视频举报）
            if selectedVideoItems.count > 0 {
                // 从个人主页举报流程进入，需要返回到个人主页
                // 查找PersonalReportViewController并返回到它的上一级
                let viewControllers = navigationController.viewControllers
                for (index, vc) in viewControllers.enumerated() {
                    if vc is PersonalReportViewController {
                        // 找到PersonalReportViewController，返回到它的上一级
                        if index > 0 {
                            navigationController.popToViewController(viewControllers[index - 1], animated: true)
                        } else {
                            // 如果PersonalReportViewController是根控制器，则pop到根
                            navigationController.popToRootViewController(animated: true)
                        }
                        return
                    }
                }
                // 如果没找到PersonalReportViewController，则pop两级
                if viewControllers.count >= 3 {
                    navigationController.popToViewController(viewControllers[viewControllers.count - 3], animated: true)
                } else {
                    navigationController.popToRootViewController(animated: true)
                }
            } else {
                // 单视频举报，只pop一级
                navigationController.popViewController(animated: true)
            }
        } else {
            // 如果是模态展示，使用dismiss
            dismiss(animated: true)
        }
    }

    private func showToast(_ message: String) {
        // 这里可以使用项目中现有的Toast组件
        let alert = UIAlertController(title: nil, message: message, preferredStyle: .alert)
        present(alert, animated: true)
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.5) {
            alert.dismiss(animated: true)
        }
    }
}

// MARK: - UITextViewDelegate

extension VideoReportViewController: UITextViewDelegate {
    func textViewDidChange(_ textView: UITextView) {
        // 更新占位符显示
        descriptionPlaceholderLabel.isHidden = !textView.text.isEmpty

        // 限制字数
        if textView.text.count > 100 {
            textView.text = String(textView.text.prefix(100))
        }

        // 更新字数统计
        descriptionCountLabel.text = "\(textView.text.count)/100"

        // 更新举报描述
        reportData.reportDescription = textView.text

        // 更新提交按钮状态
        updateSubmitButtonState()
    }
}

// MARK: - 图片选择器

extension VideoReportViewController {
    private func openImagePicker() {
        var config = PickerConfiguration()
        config.selectMode = .multiple
        config.selectOptions = .photo
        config.maximumSelectedCount = maxImageCount - selectedImages.count
        config.modalPresentationStyle = .fullScreen
        config.editor.modalPresentationStyle = .fullScreen

        // 使用HXPhotoPicker进行图片选择
        self.hx.present(
            picker: config,
            finish: { [weak self] result, _ in
                guard let self = self else { return }
                // 将选择结果转换为UIImage数组并追加到已选图片中
                result.getImage { images in
                    self.selectedImages.append(contentsOf: images)
                }
            },
            cancel: nil
        )
    }
}

// MARK: - UICollectionViewDataSource & UICollectionViewDelegate

extension VideoReportViewController: UICollectionViewDataSource, UICollectionViewDelegate {
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return selectedImages.count < maxImageCount ? selectedImages.count + 1 : selectedImages.count
    }

    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        if indexPath.item == selectedImages.count && selectedImages.count < maxImageCount {
            let cell = collectionView.dequeueReusableCell(withReuseIdentifier: "ReportAddImageCell", for: indexPath) as! ReportAddImageCell
            return cell
        } else {
            let cell = collectionView.dequeueReusableCell(withReuseIdentifier: "ReportImageCell", for: indexPath) as! ReportImageCell
            cell.configure(with: selectedImages[indexPath.item])
            cell.onDelete = { [weak self] in
                guard let self = self else { return }
                self.selectedImages.remove(at: indexPath.item)
            }
            return cell
        }
    }

    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        if indexPath.item == selectedImages.count && selectedImages.count < maxImageCount {
            openImagePicker()
        }
    }
}

// MARK: - 自定义Cell

/// 举报图片显示Cell
private class ReportImageCell: UICollectionViewCell {
    private let imageView = UIImageView()
    private let deleteButton = UIButton(type: .custom)
    var onDelete: (() -> Void)?

    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
    }

    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    private func setupUI() {
        // 图片视图 - 保持80x80尺寸，居中显示
        contentView.addSubview(imageView)
        imageView.layer.cornerRadius = 8
        imageView.clipsToBounds = true
        imageView.contentMode = .scaleAspectFill
        imageView.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.size.equalTo(CGSize(width: 80, height: 80)) // 图片保持80x80
        }

        // 删除按钮
        deleteButton.setImage(UIImage(named: "icon_video_edit_image_clear"), for: .normal)
        deleteButton.backgroundColor = .white
        deleteButton.layer.cornerRadius = 10
        deleteButton.clipsToBounds = true
        deleteButton.layer.shadowColor = UIColor.black.withAlphaComponent(0.15).cgColor
        deleteButton.layer.shadowOffset = CGSize(width: 0, height: 1)
        deleteButton.layer.shadowOpacity = 1
        deleteButton.layer.shadowRadius = 2
        deleteButton.addTarget(self, action: #selector(deleteTapped), for: .touchUpInside)
        contentView.addSubview(deleteButton)

        deleteButton.snp.makeConstraints { make in
            make.size.equalTo(CGSize(width: 20, height: 20))
            make.centerX.equalTo(imageView.snp.right).offset(-2)
            make.centerY.equalTo(imageView.snp.top).offset(3)
        }
    }

    func configure(with image: UIImage) {
        imageView.image = image
    }

    @objc private func deleteTapped() {
        onDelete?()
    }

    override func prepareForReuse() {
        super.prepareForReuse()
        imageView.image = nil
    }
}

/// 举报添加图片Cell
private class ReportAddImageCell: UICollectionViewCell {
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
    }

    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    private func setupUI() {
        // 创建80x80的背景视图，居中显示
        let backgroundView = UIView()
        backgroundView.backgroundColor = UIColor(hex: "#EDEDED")
        backgroundView.layer.cornerRadius = 8
        contentView.addSubview(backgroundView)

        backgroundView.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.size.equalTo(CGSize(width: 80, height: 80)) // 背景保持80x80
        }

        // 添加图标
        let iconImageView = UIImageView(image: UIImage(named: "icon_add_image_32"))
        iconImageView.contentMode = .scaleAspectFit
        backgroundView.addSubview(iconImageView)

        iconImageView.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.size.equalTo(CGSize(width: 32, height: 32))
        }
    }
}
