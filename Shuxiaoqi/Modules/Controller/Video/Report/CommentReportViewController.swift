//
//  CommentReportViewController.swift
//  Shu<PERSON><PERSON>qi
//
//  Created by AI Assistant on 2025/8/22.
//

import UIKit
import SnapKit
import MBProgressHUD

/// 评论举报弹窗
class CommentReportViewController: UIViewController {
    
    // MARK: - 属性
    
    /// 被举报的评论模型
    private let commentModel: CommentModel

    /// 评论所属的视频ID
    private let videoId: Int

    /// 评论举报理由数据
    private var reportReasons: [ReportCommentReasonDetail] = []
    
    /// 选中的举报理由按钮
    private var selectedReasonButton: UIButton?
    
    /// 选中的举报理由值
    private var selectedReasonValue: String = ""
    
    // MARK: - UI组件

    private lazy var backgroundView: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor.black.withAlphaComponent(0.5)
        return view
    }()

    private lazy var containerView: UIView = {
        let view = UIView()
        view.backgroundColor = .white
        view.layer.cornerRadius = 12
        view.layer.maskedCorners = [.layerMinXMinYCorner, .layerMaxXMinYCorner] // 只有顶部圆角
        view.layer.masksToBounds = true
        return view
    }()

    private lazy var titleLabel: UILabel = {
        let label = UILabel()
        label.text = "评论举报"
        label.font = UIFont.systemFont(ofSize: 18, weight: .medium)
        label.textColor = UIColor(hex: "#333333")
        label.textAlignment = .center
        return label
    }()

    private lazy var closeButton: UIButton = {
        let button = UIButton(type: .custom)
        button.setImage(UIImage(systemName: "xmark"), for: .normal)
        button.tintColor = UIColor(hex: "#999999")
        button.addTarget(self, action: #selector(closeButtonTapped), for: .touchUpInside)
        return button
    }()

    private lazy var descriptionContainer: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor(hex: "#DCEAFA")
        return view
    }()

    private lazy var descriptionLabel: UILabel = {
        let label = UILabel()
        label.text = "请选择最符合的举报原因，帮助我们准确处理"
        label.font = UIFont.systemFont(ofSize: 12)
        label.textColor = UIColor(hex: "#666666")
        label.numberOfLines = 0
        return label
    }()

    private lazy var scrollView: UIScrollView = {
        let scrollView = UIScrollView()
        scrollView.backgroundColor = .clear
        scrollView.showsVerticalScrollIndicator = false
        return scrollView
    }()

    private lazy var reasonsContainer: UIView = {
        let view = UIView()
        view.backgroundColor = .clear
        return view
    }()

    private lazy var submitButton: UIButton = {
        let button = UIButton(type: .custom)
        button.setTitle("提交", for: .normal)
        button.setTitleColor(.white, for: .normal)
        button.titleLabel?.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        button.backgroundColor = UIColor(hex: "#FFC78F") // 未选中状态的背景色
        button.layer.cornerRadius = 15
        button.isEnabled = false
        button.addTarget(self, action: #selector(submitButtonTapped), for: .touchUpInside)
        return button
    }()
    
    // MARK: - 初始化
    
    /// 初始化评论举报页面
    /// - Parameters:
    ///   - commentModel: 被举报的评论模型
    ///   - videoId: 评论所属的视频ID
    init(commentModel: CommentModel, videoId: Int) {
        self.commentModel = commentModel
        self.videoId = videoId
        super.init(nibName: nil, bundle: nil)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - 生命周期
    
    override func viewDidLoad() {
        super.viewDidLoad()

        setupUI()
        loadReportReasons()
    }

    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)

        // 弹窗从底部向上滑出动画
        animateContainerViewIn()
    }

    // MARK: - UI设置

    private func setupUI() {
        view.backgroundColor = .clear

        // 添加背景视图
        view.addSubview(backgroundView)
        backgroundView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }

        // 添加容器视图
        view.addSubview(containerView)
        containerView.snp.makeConstraints { make in
            make.left.right.equalToSuperview()
            make.bottom.equalToSuperview()
            make.height.equalTo(560) // 设计稿要求的高度：从下到上560pt
        }

        // 添加标题
        containerView.addSubview(titleLabel)
        titleLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(20)
            make.centerX.equalToSuperview()
        }

        // 添加关闭按钮
        containerView.addSubview(closeButton)
        closeButton.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(20)
            make.right.equalToSuperview().offset(-20)
            make.width.height.equalTo(24)
        }

        // 添加描述容器
        containerView.addSubview(descriptionContainer)
        descriptionContainer.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(20)
            make.left.right.equalToSuperview()
            make.height.equalTo(24) // 设计稿要求的高度
        }

        // 添加描述标签到容器中
        descriptionContainer.addSubview(descriptionLabel)
        descriptionLabel.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.left.equalToSuperview().offset(20)
            make.right.equalToSuperview().offset(-20)
        }

        // 添加滚动视图
        containerView.addSubview(scrollView)
        scrollView.snp.makeConstraints { make in
            make.top.equalTo(descriptionContainer.snp.bottom)
            make.left.right.equalToSuperview()
            make.bottom.equalToSuperview().offset(-80) // 为底部按钮留空间
        }

        // 添加举报理由容器到滚动视图
        scrollView.addSubview(reasonsContainer)
        reasonsContainer.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.width.equalTo(scrollView)
        }

        // 添加提交按钮
        containerView.addSubview(submitButton)
        submitButton.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(18)
            make.right.equalToSuperview().offset(-18)
            make.bottom.equalToSuperview().offset(-26) // 距离安全距离-12pt再上移14pt = -26pt
            make.height.equalTo(30) // 设计稿要求的高度
        }

        // 添加背景点击手势
        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(backgroundTapped))
        backgroundView.addGestureRecognizer(tapGesture)

        // 初始状态：容器视图在屏幕底部外，背景透明
        containerView.transform = CGAffineTransform(translationX: 0, y: 560)
        backgroundView.alpha = 0.0
    }
    
    // MARK: - 数据加载
    
    private func loadReportReasons() {
        // 显示加载状态
        showLoading()

        APIManager.shared.getReportCommentReasonDict { [weak self] result in
            DispatchQueue.main.async {
                self?.hideLoading()

                switch result {
                case .success(let response):
                    if response.isSuccess {
                        self?.reportReasons = response.data?.dictDetails.sorted { $0.dictSort < $1.dictSort } ?? []
                        self?.setupReasonButtons()
                    } else {
                        self?.showErrorAlert(message: response.displayMessage)
                    }
                case .failure(let error):
                    self?.showErrorAlert(message: error.errorMessage)
                }
            }
        }
    }
    
    private func setupReasonButtons() {
        // 清除现有按钮
        reasonsContainer.subviews.forEach { $0.removeFromSuperview() }
        
        var lastView: UIView? = nil
        
        for (index, reason) in reportReasons.enumerated() {
            let button = createReasonButton(reason: reason, index: index)
            reasonsContainer.addSubview(button)
            
            button.snp.makeConstraints { make in
                make.left.right.equalToSuperview()
                make.height.equalTo(42) // 设计稿要求的高度

                if let lastView = lastView {
                    make.top.equalTo(lastView.snp.bottom)
                } else {
                    make.top.equalToSuperview()
                }
            }
            
            lastView = button
        }
        
        // 设置容器高度
        if let lastView = lastView {
            reasonsContainer.snp.makeConstraints { make in
                make.bottom.equalTo(lastView.snp.bottom)
            }
        }
    }
    
    private func createReasonButton(reason: ReportCommentReasonDetail, index: Int) -> UIButton {
        let button = UIButton(type: .custom)
        button.setTitle(reason.label, for: .normal)
        button.setTitleColor(UIColor(hex: "#333333"), for: .normal)
        button.titleLabel?.font = UIFont.systemFont(ofSize: 16)
        button.backgroundColor = .white
        button.contentHorizontalAlignment = .left
        button.titleEdgeInsets = UIEdgeInsets(top: 0, left: 20, bottom: 0, right: 20)
        button.tag = index
        button.accessibilityIdentifier = reason.value
        button.addTarget(self, action: #selector(reasonButtonTapped(_:)), for: .touchUpInside)

        // 调试日志
        print("🔍 创建举报理由按钮: \(reason.label) -> \(reason.value)")

        // 添加下划线
        let bottomLine = UIView()
        bottomLine.backgroundColor = UIColor(hex: "#E5E5E5")
        button.addSubview(bottomLine)
        bottomLine.snp.makeConstraints { make in
            make.left.right.bottom.equalToSuperview()
            make.height.equalTo(1)
        }

        // 添加选中状态的图标
        let checkImageView = UIImageView()
        checkImageView.image = UIImage(named: "user_info_edit_list_select")
        checkImageView.isHidden = true
        checkImageView.tag = 1000 + index
        button.addSubview(checkImageView)

        checkImageView.snp.makeConstraints { make in
            make.right.equalToSuperview().offset(-20)
            make.centerY.equalToSuperview()
            make.width.height.equalTo(16) // 设计稿要求的尺寸
        }

        return button
    }
    
    // MARK: - 事件处理

    @objc private func closeButtonTapped() {
        dismissWithAnimation()
    }

    @objc private func backgroundTapped() {
        dismissWithAnimation()
    }

    @objc private func reasonButtonTapped(_ sender: UIButton) {
        // 取消之前选中的按钮
        if let previousButton = selectedReasonButton {
            previousButton.backgroundColor = .white
            previousButton.setTitleColor(UIColor(hex: "#333333"), for: .normal)
            if let checkImageView = previousButton.viewWithTag(1000 + previousButton.tag) as? UIImageView {
                checkImageView.isHidden = true
            }
        }

        // 设置当前按钮为选中状态
        sender.backgroundColor = .white
        sender.setTitleColor(UIColor(hex: "#FF8F1F"), for: .normal)
        if let checkImageView = sender.viewWithTag(1000 + sender.tag) as? UIImageView {
            checkImageView.image = UIImage(named: "user_info_edit_list_select")
            checkImageView.isHidden = false
        }

        selectedReasonButton = sender
        selectedReasonValue = sender.accessibilityIdentifier ?? ""

        // 调试日志
        print("🔍 选中举报理由: \(sender.title(for: .normal) ?? "无") -> \(selectedReasonValue)")

        // 更新提交按钮状态
        updateSubmitButtonState()
    }
    
    @objc private func submitButtonTapped() {
        guard !selectedReasonValue.isEmpty else {
            showToast("请选择举报理由")
            return
        }
        
        submitReport()
    }
    
    private func updateSubmitButtonState() {
        let isEnabled = !selectedReasonValue.isEmpty
        submitButton.isEnabled = isEnabled

        if isEnabled {
            submitButton.backgroundColor = UIColor(hex: "#FF8F1F") // 可交互背景色
        } else {
            submitButton.backgroundColor = UIColor(hex: "#FFC78F") // 没勾选的背景色
        }
    }
    
    // MARK: - 提交举报
    
    private func submitReport() {
        // 使用举报类型管理器构建请求数据
        var builder = ReportTypeManager.CommentReportBuilder()
        builder.commentId = commentModel.id
        builder.reportedUserId = commentModel.customerId
        builder.reportedUserName = commentModel.username
        builder.reportedUserAvatar = commentModel.avatar
        builder.selectedReasonValue = selectedReasonValue

        let reportRequest = builder.build()

        // 验证请求数据
        let validation = ReportTypeManager.shared.validateReportRequest(reportRequest)
        guard validation.isValid else {
            showErrorAlert(message: validation.errorMessage)
            return
        }

        // 打印调试信息
        print("🔍 评论举报参数 (不包含视频ID):")
        ReportTypeManager.shared.printDebugInfo(for: reportRequest)

        // 显示加载状态
        showLoading()

        APIManager.shared.submitReport(reportData: reportRequest) { [weak self] result in
            DispatchQueue.main.async {
                self?.hideLoading()

                switch result {
                case .success(let response):
                    if response.isSuccess {
                        self?.showSuccessAlert()
                    } else {
                        print("❌ 评论举报失败: \(response.displayMessage)")
                        self?.showErrorAlert(message: response.displayMessage)
                    }
                case .failure(let error):
                    print("❌ 评论举报网络错误: \(error.errorMessage)")
                    self?.showErrorAlert(message: error.errorMessage)
                }
            }
        }
    }
    
    private func showSuccessAlert() {
        showToast("举报成功，感谢您的举报")

        // 2秒后自动关闭弹窗
        DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) { [weak self] in
            self?.dismissWithAnimation()
        }
    }
    
    private func showErrorAlert(message: String) {
        let alert = UIAlertController(title: "举报失败", message: message, preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "确定", style: .default))
        present(alert, animated: true)
    }

    // MARK: - 辅助方法

    private func showLoading() {
        MBProgressHUD.showAdded(to: self.view, animated: true)
    }

    private func hideLoading() {
        MBProgressHUD.hide(for: self.view, animated: true)
    }

    private func showToast(_ message: String) {
        let hud = MBProgressHUD.showAdded(to: self.view, animated: true)
        hud.mode = .text
        hud.label.text = message
        hud.hide(animated: true, afterDelay: 2.0)
    }

    // MARK: - 动画方法

    private func animateContainerViewIn() {
        UIView.animate(withDuration: 0.3, delay: 0, options: .curveEaseOut) {
            self.containerView.transform = .identity
            self.backgroundView.alpha = 1.0
        }
    }

    private func dismissWithAnimation() {
        UIView.animate(withDuration: 0.3, delay: 0, options: .curveEaseIn) {
            self.containerView.transform = CGAffineTransform(translationX: 0, y: 560)
            self.backgroundView.alpha = 0.0
        } completion: { _ in
            self.dismiss(animated: false)
        }
    }
}
