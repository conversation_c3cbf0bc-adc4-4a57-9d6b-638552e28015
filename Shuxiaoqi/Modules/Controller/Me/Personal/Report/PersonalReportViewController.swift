import UIKit
import Kingfisher
import MBProgressHUD

/// 个人主页举报类型
enum PersonalReportType {
    case userInfo   // 个人信息举报
    case works      // 作品举报
}

/// 个人主页举报控制器
class PersonalReportViewController: BaseViewController {
    
    // MARK: - 属性
    private var userId: String
    private var userName: String
    private var userAvatar: String
    private var currentReportType: PersonalReportType?

    // UI组件
    private var scrollView: UIScrollView!
    private var scrollContentView: UIView!
    private var reportTypeContainerView: UIView!
    private var userInfoReportButton: UIButton!
    private var worksReportButton: UIButton!
    private var userInfoRadioButton: UIButton!
    private var worksRadioButton: UIButton!

    private var contentContainerView: UIView!
    private var submitButton: UIButton!

    // 个人信息举报相关
    private var reportContentContainerView: UIView!
    private var reportReasonContainerView: UIView!
    private var reportContentData: [ReportUserInfoContentDetail] = []
    private var reportReasonData: [ReportUserInfoReasonDetail] = []
    private var selectedContentIndices: Set<Int> = [] // 改为多选
    private var selectedReasonIndex: Int?

    // 作品举报相关
    private var worksListData: [VideoItem] = []
    private var selectedWorksIndices: Set<Int> = []
    private var worksListContainerView: UIView!
    
    // MARK: - 初始化
    init(userId: String, userName: String = "", userAvatar: String = "", worksData: [VideoItem] = []) {
        self.userId = userId
        self.userName = userName
        self.userAvatar = userAvatar
        self.worksListData = worksData
        super.init(nibName: nil, bundle: nil)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - 生命周期
    override func viewDidLoad() {
        super.viewDidLoad()
        
        // 设置导航栏标题
        navTitle = "举报类型"
        
        setupUI()
        loadReportData()

        // 设置初始状态
        updateSubmitButtonState()
    }
    

    private func setupUI() {
        contentView.backgroundColor = .white
        
        // 提交按钮容器（白色背景，防止内容穿透）
        let submitButtonContainer = UIView()
        submitButtonContainer.backgroundColor = .white
        contentView.addSubview(submitButtonContainer)
        submitButtonContainer.translatesAutoresizingMaskIntoConstraints = false
        NSLayoutConstraint.activate([
            submitButtonContainer.leftAnchor.constraint(equalTo: contentView.leftAnchor),
            submitButtonContainer.rightAnchor.constraint(equalTo: contentView.rightAnchor),
            submitButtonContainer.bottomAnchor.constraint(equalTo: contentView.bottomAnchor),
            submitButtonContainer.heightAnchor.constraint(equalToConstant: 57) // 43(按钮高度) + 14(间距)
        ])
        
        // 滚动视图
        scrollView = UIScrollView()
        scrollView.showsVerticalScrollIndicator = false
        contentView.addSubview(scrollView)
        scrollView.translatesAutoresizingMaskIntoConstraints = false
        NSLayoutConstraint.activate([
            scrollView.topAnchor.constraint(equalTo: contentView.topAnchor, constant: 16),
            scrollView.leftAnchor.constraint(equalTo: contentView.leftAnchor),
            scrollView.rightAnchor.constraint(equalTo: contentView.rightAnchor),
            scrollView.bottomAnchor.constraint(equalTo: submitButtonContainer.topAnchor)
        ])
        
        // 内容视图
        scrollContentView = UIView()
        scrollView.addSubview(scrollContentView)
        scrollContentView.translatesAutoresizingMaskIntoConstraints = false
        NSLayoutConstraint.activate([
            scrollContentView.topAnchor.constraint(equalTo: scrollView.topAnchor),
            scrollContentView.leftAnchor.constraint(equalTo: scrollView.leftAnchor),
            scrollContentView.rightAnchor.constraint(equalTo: scrollView.rightAnchor),
            scrollContentView.bottomAnchor.constraint(equalTo: scrollView.bottomAnchor),
            scrollContentView.widthAnchor.constraint(equalTo: scrollView.widthAnchor)
        ])
        
        setupReportTypeSelection()
        setupContentContainer()
        setupSubmitButton()
        
        // 设置内容视图高度约束
        let heightConstraint = scrollContentView.heightAnchor.constraint(greaterThanOrEqualTo: view.heightAnchor, constant: -view.safeAreaInsets.top - view.safeAreaInsets.bottom)
        heightConstraint.priority = UILayoutPriority(999)
        heightConstraint.isActive = true
    }
    
    private func setupReportTypeSelection() {
        // 举报类型容器（带阴影）
        reportTypeContainerView = createShadowContainer()
        scrollContentView.addSubview(reportTypeContainerView)
        reportTypeContainerView.translatesAutoresizingMaskIntoConstraints = false
        NSLayoutConstraint.activate([
            reportTypeContainerView.topAnchor.constraint(equalTo: scrollContentView.topAnchor, constant: 0),
            reportTypeContainerView.leftAnchor.constraint(equalTo: scrollContentView.leftAnchor, constant: 16),
            reportTypeContainerView.rightAnchor.constraint(equalTo: scrollContentView.rightAnchor, constant: -16),
            reportTypeContainerView.heightAnchor.constraint(equalToConstant: 160)
        ])
        
        // 个人信息举报选项
        setupReportOption(
            in: reportTypeContainerView,
            title: "个人信息举报",
            subtitle: "头像、昵称等信息违规",
            isTop: true,
            button: &userInfoReportButton,
            radioButton: &userInfoRadioButton,
            action: #selector(userInfoReportTapped)
        )
        
        // 分割线
        let separatorLine = UIView()
        separatorLine.backgroundColor = UIColor(hex: "#E7E7E7", alpha: 0.6)
        reportTypeContainerView.addSubview(separatorLine)
        separatorLine.translatesAutoresizingMaskIntoConstraints = false
        NSLayoutConstraint.activate([
            separatorLine.centerYAnchor.constraint(equalTo: reportTypeContainerView.centerYAnchor),
            separatorLine.leftAnchor.constraint(equalTo: reportTypeContainerView.leftAnchor, constant: 16),
            separatorLine.rightAnchor.constraint(equalTo: reportTypeContainerView.rightAnchor, constant: -16),
            separatorLine.heightAnchor.constraint(equalToConstant: 1)
        ])
        
        // 作品举报选项
        setupReportOption(
            in: reportTypeContainerView,
            title: "作品举报",
            subtitle: "针对该账号盗播或发布的不正当内容举报",
            isTop: false,
            button: &worksReportButton,
            radioButton: &worksRadioButton,
            action: #selector(worksReportTapped)
        )
        
        // 初始状态：不选择任何选项
        // 注意：此时radio按钮已经创建完成，可以安全调用updateReportTypeSelection
        updateReportTypeSelection()
    }
    
    private func setupContentContainer() {
        contentContainerView = UIView()
        scrollContentView.addSubview(contentContainerView)
        contentContainerView.translatesAutoresizingMaskIntoConstraints = false
        NSLayoutConstraint.activate([
            contentContainerView.topAnchor.constraint(equalTo: reportTypeContainerView.bottomAnchor, constant: 16),
            contentContainerView.leftAnchor.constraint(equalTo: scrollContentView.leftAnchor),
            contentContainerView.rightAnchor.constraint(equalTo: scrollContentView.rightAnchor),
            contentContainerView.bottomAnchor.constraint(equalTo: scrollContentView.bottomAnchor, constant: -100)
        ])
        
        // 初始状态不设置内容，等待用户选择举报类型
        // 隐藏内容容器
        contentContainerView.isHidden = true
    }
    
    private func setupSubmitButton() {
        submitButton = UIButton(type: .custom)
        submitButton.setTitle("提交", for: .normal)
        submitButton.setTitleColor(.white, for: .normal)
        submitButton.titleLabel?.font = .systemFont(ofSize: 16, weight: .medium)
        submitButton.backgroundColor = UIColor(hex: "#E5E5E5") // 默认不可交互状态
        submitButton.layer.cornerRadius = 6
        submitButton.isEnabled = false
        submitButton.addTarget(self, action: #selector(submitButtonTapped), for: .touchUpInside)
        
        // 找到submitButtonContainer并添加按钮
        if let submitButtonContainer = contentView.subviews.first(where: { $0.backgroundColor == .white && $0 != contentView }) {
            submitButtonContainer.addSubview(submitButton)
            submitButton.translatesAutoresizingMaskIntoConstraints = false
            NSLayoutConstraint.activate([
                submitButton.leftAnchor.constraint(equalTo: submitButtonContainer.leftAnchor, constant: 16),
                submitButton.rightAnchor.constraint(equalTo: submitButtonContainer.rightAnchor, constant: -16),
                submitButton.bottomAnchor.constraint(equalTo: submitButtonContainer.bottomAnchor, constant: -14),
                submitButton.heightAnchor.constraint(equalToConstant: 43)
            ])
        }
    }

    // MARK: - 辅助方法
    private func createShadowContainer() -> UIView {
        let containerView = UIView()
        containerView.backgroundColor = .white
        containerView.layer.cornerRadius = 8
        containerView.layer.shadowColor = UIColor(red: 0, green: 0, blue: 0, alpha: 0.1).cgColor
        containerView.layer.shadowOffset = CGSize(width: 0, height: 2)
        containerView.layer.shadowOpacity = 1
        containerView.layer.shadowRadius = 4
        return containerView
    }

    private func setupReportOption(in container: UIView, title: String, subtitle: String, isTop: Bool, button: inout UIButton!, radioButton: inout UIButton!, action: Selector) {
        button = UIButton(type: .custom)
        button.addTarget(self, action: action, for: .touchUpInside)
        container.addSubview(button)

        // 单选按钮
        radioButton = UIButton(type: .custom)
        radioButton.setImage(UIImage(named: "app_radio_Default"), for: .normal)
        radioButton.setImage(UIImage(named: "app_radio_select"), for: .selected)
        radioButton.isUserInteractionEnabled = false
        button.addSubview(radioButton)

        // 标题
        let titleLabel = UILabel()
        titleLabel.text = title
        titleLabel.font = .systemFont(ofSize: 16, weight: .medium)
        titleLabel.textColor = UIColor(hex: "#333333")
        button.addSubview(titleLabel)

        // 副标题
        let subtitleLabel = UILabel()
        subtitleLabel.text = subtitle
        subtitleLabel.font = .systemFont(ofSize: 12)
        subtitleLabel.textColor = UIColor(hex: "#999999")
        subtitleLabel.numberOfLines = 0
        button.addSubview(subtitleLabel)

        // 布局
        button.translatesAutoresizingMaskIntoConstraints = false
        radioButton.translatesAutoresizingMaskIntoConstraints = false
        titleLabel.translatesAutoresizingMaskIntoConstraints = false
        subtitleLabel.translatesAutoresizingMaskIntoConstraints = false

        let topConstant: CGFloat = isTop ? 16 : 8
        let bottomConstant: CGFloat = isTop ? -8 : -16

        NSLayoutConstraint.activate([
            button.topAnchor.constraint(equalTo: isTop ? container.topAnchor : container.centerYAnchor, constant: topConstant),
            button.leftAnchor.constraint(equalTo: container.leftAnchor),
            button.rightAnchor.constraint(equalTo: container.rightAnchor),
            button.bottomAnchor.constraint(equalTo: isTop ? container.centerYAnchor : container.bottomAnchor, constant: bottomConstant),

            radioButton.rightAnchor.constraint(equalTo: button.rightAnchor, constant: -16),
            radioButton.centerYAnchor.constraint(equalTo: button.centerYAnchor),
            radioButton.widthAnchor.constraint(equalToConstant: 20),
            radioButton.heightAnchor.constraint(equalToConstant: 20),

            titleLabel.leftAnchor.constraint(equalTo: button.leftAnchor, constant: 16),
            titleLabel.topAnchor.constraint(equalTo: button.topAnchor, constant: 8),
            titleLabel.rightAnchor.constraint(equalTo: radioButton.leftAnchor, constant: -8),

            subtitleLabel.leftAnchor.constraint(equalTo: titleLabel.leftAnchor),
            subtitleLabel.topAnchor.constraint(equalTo: titleLabel.bottomAnchor, constant: 4),
            subtitleLabel.rightAnchor.constraint(equalTo: titleLabel.rightAnchor),
            subtitleLabel.bottomAnchor.constraint(lessThanOrEqualTo: button.bottomAnchor, constant: -8)
        ])
    }

    private func setupUserInfoReportContent() {
        // 清空容器
        contentContainerView.subviews.forEach { $0.removeFromSuperview() }

        // 举报内容容器
        reportContentContainerView = createShadowContainer()
        contentContainerView.addSubview(reportContentContainerView)
        reportContentContainerView.translatesAutoresizingMaskIntoConstraints = false

        // 举报理由容器
        reportReasonContainerView = createShadowContainer()
        contentContainerView.addSubview(reportReasonContainerView)
        reportReasonContainerView.translatesAutoresizingMaskIntoConstraints = false

        NSLayoutConstraint.activate([
            reportContentContainerView.topAnchor.constraint(equalTo: contentContainerView.topAnchor),
            reportContentContainerView.leftAnchor.constraint(equalTo: contentContainerView.leftAnchor, constant: 16),
            reportContentContainerView.rightAnchor.constraint(equalTo: contentContainerView.rightAnchor, constant: -16),

            reportReasonContainerView.topAnchor.constraint(equalTo: reportContentContainerView.bottomAnchor, constant: 16),
            reportReasonContainerView.leftAnchor.constraint(equalTo: contentContainerView.leftAnchor, constant: 16),
            reportReasonContainerView.rightAnchor.constraint(equalTo: contentContainerView.rightAnchor, constant: -16),
            reportReasonContainerView.bottomAnchor.constraint(lessThanOrEqualTo: contentContainerView.bottomAnchor)
        ])

        setupReportContentSection()
        setupReportReasonSection()

        // 如果数据已经加载完成，立即更新UI
        if !reportContentData.isEmpty {
            updateReportContentUI()
        }
        if !reportReasonData.isEmpty {
            updateReportReasonUI()
        }
    }

    private func setupReportContentSection() {
        // 标题
        let titleLabel = UILabel()
        titleLabel.text = "举报内容"
        titleLabel.font = .systemFont(ofSize: 16, weight: .medium)
        titleLabel.textColor = UIColor(hex: "#333333")
        reportContentContainerView.addSubview(titleLabel)

        let subtitleLabel = UILabel()
        subtitleLabel.text = "(可多选)"
        subtitleLabel.font = .systemFont(ofSize: 12)
        subtitleLabel.textColor = UIColor(hex: "#999999")
        reportContentContainerView.addSubview(subtitleLabel)

        titleLabel.translatesAutoresizingMaskIntoConstraints = false
        subtitleLabel.translatesAutoresizingMaskIntoConstraints = false

        NSLayoutConstraint.activate([
            titleLabel.topAnchor.constraint(equalTo: reportContentContainerView.topAnchor, constant: 16),
            titleLabel.leftAnchor.constraint(equalTo: reportContentContainerView.leftAnchor, constant: 16),

            subtitleLabel.leftAnchor.constraint(equalTo: titleLabel.rightAnchor, constant: 4),
            subtitleLabel.centerYAnchor.constraint(equalTo: titleLabel.centerYAnchor)
        ])
    }

    private func setupReportReasonSection() {
        // 标题
        let titleLabel = UILabel()
        titleLabel.text = "举报理由"
        titleLabel.font = .systemFont(ofSize: 16, weight: .medium)
        titleLabel.textColor = UIColor(hex: "#333333")
        reportReasonContainerView.addSubview(titleLabel)

        let subtitleLabel = UILabel()
        subtitleLabel.text = "(仅单选)"
        subtitleLabel.font = .systemFont(ofSize: 12)
        subtitleLabel.textColor = UIColor(hex: "#999999")
        reportReasonContainerView.addSubview(subtitleLabel)

        titleLabel.translatesAutoresizingMaskIntoConstraints = false
        subtitleLabel.translatesAutoresizingMaskIntoConstraints = false

        NSLayoutConstraint.activate([
            titleLabel.topAnchor.constraint(equalTo: reportReasonContainerView.topAnchor, constant: 16),
            titleLabel.leftAnchor.constraint(equalTo: reportReasonContainerView.leftAnchor, constant: 16),

            subtitleLabel.leftAnchor.constraint(equalTo: titleLabel.rightAnchor, constant: 4),
            subtitleLabel.centerYAnchor.constraint(equalTo: titleLabel.centerYAnchor)
        ])
    }



    // MARK: - 事件处理
    @objc private func userInfoReportTapped() {
        currentReportType = .userInfo
        updateReportTypeSelection()
        contentContainerView.isHidden = false
        setupUserInfoReportContent()
        updateSubmitButtonState()
    }

    @objc private func worksReportTapped() {
        currentReportType = .works
        updateReportTypeSelection()
        contentContainerView.isHidden = false
        setupWorksReportContent()
        updateSubmitButtonState()
    }

    @objc private func submitButtonTapped() {
        guard let reportType = currentReportType else { return }

        switch reportType {
        case .userInfo:
            submitUserInfoReport()
        case .works:
            submitWorksReport()
        }
    }

    private func updateReportTypeSelection() {
        // 安全检查，确保按钮已经初始化
        guard let userInfoRadio = userInfoRadioButton,
              let worksRadio = worksRadioButton else {
            return
        }

        userInfoRadio.isSelected = (currentReportType == .userInfo)
        worksRadio.isSelected = (currentReportType == .works)
    }

    private func setupWorksReportContent() {
        // 清空容器
        contentContainerView.subviews.forEach { $0.removeFromSuperview() }

        // 标题（放在容器外面）
        let titleLabel = UILabel()
        titleLabel.text = "举报作品"
        titleLabel.font = .systemFont(ofSize: 16, weight: .medium)
        titleLabel.textColor = UIColor(hex: "#333333")
        contentContainerView.addSubview(titleLabel)

        let subtitleLabel = UILabel()
        subtitleLabel.text = "(可多选)"
        subtitleLabel.font = .systemFont(ofSize: 12)
        subtitleLabel.textColor = UIColor(hex: "#999999")
        contentContainerView.addSubview(subtitleLabel)

        titleLabel.translatesAutoresizingMaskIntoConstraints = false
        subtitleLabel.translatesAutoresizingMaskIntoConstraints = false

        NSLayoutConstraint.activate([
            titleLabel.topAnchor.constraint(equalTo: contentContainerView.topAnchor, constant: 16),
            titleLabel.leftAnchor.constraint(equalTo: contentContainerView.leftAnchor, constant: 16),

            subtitleLabel.leftAnchor.constraint(equalTo: titleLabel.rightAnchor, constant: 4),
            subtitleLabel.centerYAnchor.constraint(equalTo: titleLabel.centerYAnchor)
        ])

        // 作品列表容器（用于放置独立的作品卡片）
        worksListContainerView = UIView()
        contentContainerView.addSubview(worksListContainerView)
        worksListContainerView.translatesAutoresizingMaskIntoConstraints = false

        NSLayoutConstraint.activate([
            worksListContainerView.topAnchor.constraint(equalTo: titleLabel.bottomAnchor, constant: 16),
            worksListContainerView.leftAnchor.constraint(equalTo: contentContainerView.leftAnchor),
            worksListContainerView.rightAnchor.constraint(equalTo: contentContainerView.rightAnchor),
            worksListContainerView.bottomAnchor.constraint(lessThanOrEqualTo: contentContainerView.bottomAnchor)
        ])

        // 如果作品数据已经加载，立即更新UI
        if !worksListData.isEmpty {
            updateWorksListUI()
        }

        // 更新提交按钮文字
        submitButton.setTitle("下一步", for: .normal)
    }

    private func updateSubmitButtonState() {
        let isEnabled: Bool
        let backgroundColor: UIColor

        guard let reportType = currentReportType else {
            // 没有选择举报类型时，按钮禁用且为灰色
            submitButton.isEnabled = false
            submitButton.backgroundColor = UIColor(hex: "#E5E5E5")
            submitButton.setTitle("提交", for: .normal)
            return
        }

        switch reportType {
        case .userInfo:
            isEnabled = !selectedContentIndices.isEmpty && selectedReasonIndex != nil
            backgroundColor = isEnabled ? UIColor(hex: "#FF8F1F") : UIColor(hex: "#E5E5E5")
            submitButton.setTitle("提交", for: .normal)
        case .works:
            isEnabled = selectedWorksIndices.count > 0
            backgroundColor = isEnabled ? UIColor(hex: "#FF8F1F") : UIColor(hex: "#E5E5E5")
            submitButton.setTitle("下一步", for: .normal)
        }

        submitButton.isEnabled = isEnabled
        submitButton.backgroundColor = backgroundColor
    }

    // MARK: - 数据加载
    private func loadReportData() {
        loadReportContentData()
        loadReportReasonData()
    }

    private func loadReportContentData() {
        APIManager.shared.getReportUserInfoContentDict { [weak self] result in
            DispatchQueue.main.async {
                switch result {
                case .success(let response):
                    if let data = response.data {
                        self?.reportContentData = data.dictDetails.sorted { $0.dictSort < $1.dictSort }
                        self?.updateReportContentUI()
                    }
                case .failure(let error):
                    print("获取举报内容数据失败: \(error)")
                }
            }
        }
    }

    private func loadReportReasonData() {
        APIManager.shared.getReportUserInfoReasonDict { [weak self] result in
            DispatchQueue.main.async {
                switch result {
                case .success(let response):
                    if let data = response.data {
                        self?.reportReasonData = data.dictDetails.sorted { $0.dictSort < $1.dictSort }
                        self?.updateReportReasonUI()
                    }
                case .failure(let error):
                    print("获取举报理由数据失败: \(error)")
                }
            }
        }
    }

    private func updateReportContentUI() {
        // 确保容器存在
        guard reportContentContainerView != nil else { return }

        // 移除现有的选项按钮
        reportContentContainerView.subviews.filter { $0.tag >= 1000 }.forEach { $0.removeFromSuperview() }

        var lastView: UIView? = reportContentContainerView.subviews.first { $0 is UILabel }

        for (index, content) in reportContentData.enumerated() {
            let optionButton = createOptionButton(title: content.label, isSelected: false, tag: 1000 + index)
            optionButton.addTarget(self, action: #selector(reportContentOptionTapped(_:)), for: .touchUpInside)
            reportContentContainerView.addSubview(optionButton)

            optionButton.translatesAutoresizingMaskIntoConstraints = false
            NSLayoutConstraint.activate([
                optionButton.topAnchor.constraint(equalTo: lastView?.bottomAnchor ?? reportContentContainerView.topAnchor, constant: lastView != nil ? 12 : 48),
                optionButton.leftAnchor.constraint(equalTo: reportContentContainerView.leftAnchor, constant: 16),
                optionButton.rightAnchor.constraint(equalTo: reportContentContainerView.rightAnchor, constant: -16),
                optionButton.heightAnchor.constraint(equalToConstant: 44)
            ])

            lastView = optionButton
        }

        // 更新容器高度
        if let lastView = lastView {
            lastView.bottomAnchor.constraint(equalTo: reportContentContainerView.bottomAnchor, constant: -16).isActive = true
        }
    }

    private func updateReportReasonUI() {
        // 确保容器存在
        guard reportReasonContainerView != nil else { return }

        // 移除现有的选项按钮
        reportReasonContainerView.subviews.filter { $0.tag >= 2000 }.forEach { $0.removeFromSuperview() }

        var lastView: UIView? = reportReasonContainerView.subviews.first { $0 is UILabel }

        for (index, reason) in reportReasonData.enumerated() {
            let optionButton = createOptionButton(title: reason.label, isSelected: false, tag: 2000 + index)
            optionButton.addTarget(self, action: #selector(reportReasonOptionTapped(_:)), for: .touchUpInside)
            reportReasonContainerView.addSubview(optionButton)

            optionButton.translatesAutoresizingMaskIntoConstraints = false
            NSLayoutConstraint.activate([
                optionButton.topAnchor.constraint(equalTo: lastView?.bottomAnchor ?? reportReasonContainerView.topAnchor, constant: lastView != nil ? 12 : 48),
                optionButton.leftAnchor.constraint(equalTo: reportReasonContainerView.leftAnchor, constant: 16),
                optionButton.rightAnchor.constraint(equalTo: reportReasonContainerView.rightAnchor, constant: -16),
                optionButton.heightAnchor.constraint(equalToConstant: 44)
            ])

            lastView = optionButton
        }

        // 更新容器高度
        if let lastView = lastView {
            lastView.bottomAnchor.constraint(equalTo: reportReasonContainerView.bottomAnchor, constant: -16).isActive = true
        }
    }

    private func createOptionButton(title: String, isSelected: Bool, tag: Int) -> UIButton {
        let button = UIButton(type: .custom)
        button.tag = tag

        // 单选/多选按钮
        let radioButton = UIButton(type: .custom)
        radioButton.setImage(UIImage(named: "app_radio_Default"), for: .normal)
        radioButton.setImage(UIImage(named: "app_radio_select"), for: .selected)
        radioButton.isSelected = isSelected
        radioButton.isUserInteractionEnabled = false
        button.addSubview(radioButton)

        // 标题
        let titleLabel = UILabel()
        titleLabel.text = title
        titleLabel.font = .systemFont(ofSize: 16)
        titleLabel.textColor = UIColor(hex: "#333333")
        button.addSubview(titleLabel)

        // 布局
        radioButton.translatesAutoresizingMaskIntoConstraints = false
        titleLabel.translatesAutoresizingMaskIntoConstraints = false

        NSLayoutConstraint.activate([
            radioButton.leftAnchor.constraint(equalTo: button.leftAnchor),
            radioButton.centerYAnchor.constraint(equalTo: button.centerYAnchor),
            radioButton.widthAnchor.constraint(equalToConstant: 20),
            radioButton.heightAnchor.constraint(equalToConstant: 20),

            titleLabel.leftAnchor.constraint(equalTo: radioButton.rightAnchor, constant: 12),
            titleLabel.centerYAnchor.constraint(equalTo: button.centerYAnchor),
            titleLabel.rightAnchor.constraint(lessThanOrEqualTo: button.rightAnchor)
        ])

        return button
    }

    private func updateWorksListUI() {
        // 确保容器存在
        guard worksListContainerView != nil else { return }

        // 移除现有的作品选项
        worksListContainerView.subviews.filter { $0.tag >= 3000 }.forEach { $0.removeFromSuperview() }

        var lastView: UIView? = nil

        for (index, work) in worksListData.enumerated() {
            let workItemView = createWorkItemView(work: work, index: index)
            workItemView.tag = 3000 + index
            worksListContainerView.addSubview(workItemView)

            workItemView.translatesAutoresizingMaskIntoConstraints = false
            NSLayoutConstraint.activate([
                workItemView.topAnchor.constraint(equalTo: lastView?.bottomAnchor ?? worksListContainerView.topAnchor, constant: lastView != nil ? 12 : 0),
                workItemView.leftAnchor.constraint(equalTo: worksListContainerView.leftAnchor, constant: 16),
                workItemView.rightAnchor.constraint(equalTo: worksListContainerView.rightAnchor, constant: -16),
                workItemView.heightAnchor.constraint(equalToConstant: 198)
            ])

            lastView = workItemView
        }

        // 更新容器高度
        if let lastView = lastView {
            lastView.bottomAnchor.constraint(equalTo: worksListContainerView.bottomAnchor, constant: 0).isActive = true
        }
    }

    private func createWorkItemView(work: VideoItem, index: Int) -> UIView {
        let containerView = UIView()
        containerView.backgroundColor = .white
        containerView.layer.cornerRadius = 8
        containerView.layer.shadowColor = UIColor(red: 0, green: 0, blue: 0, alpha: 0.1).cgColor
        containerView.layer.shadowOffset = CGSize(width: 0, height: 2)
        containerView.layer.shadowOpacity = 1
        containerView.layer.shadowRadius = 4

        // 选择按钮
        let selectButton = UIButton(type: .custom)
        selectButton.setImage(UIImage(named: "app_radio_Default"), for: .normal)
        selectButton.setImage(UIImage(named: "app_radio_select"), for: .selected)
        selectButton.isSelected = selectedWorksIndices.contains(index)
        selectButton.addTarget(self, action: #selector(workItemSelectTapped(_:)), for: .touchUpInside)
        selectButton.tag = index
        containerView.addSubview(selectButton)

        // 作品封面
        let coverImageView = UIImageView()
        coverImageView.contentMode = .scaleAspectFill
        coverImageView.clipsToBounds = true
        coverImageView.layer.cornerRadius = 6
        coverImageView.backgroundColor = UIColor(hex: "#F5F5F5")
        if let coverImg = work.worksCoverImg, !coverImg.isEmpty {
            coverImageView.kf.setImage(with: URL(string: coverImg), placeholder: UIImage(named: "placeholder_image"))
        }
        containerView.addSubview(coverImageView)

        // 时长标签（如果是视频）
        let durationLabel = UILabel()
        if let duration = work.duration, duration > 0 {
            let minutes = duration / 60
            let seconds = duration % 60
            durationLabel.text = String(format: "%02d:%02d", minutes, seconds)
            durationLabel.font = .systemFont(ofSize: 12)
            durationLabel.textColor = .white
            durationLabel.backgroundColor = UIColor.black.withAlphaComponent(0.6)
            durationLabel.textAlignment = .center
            durationLabel.layer.cornerRadius = 4
            durationLabel.clipsToBounds = true
            containerView.addSubview(durationLabel)
        }

        // 作品标题
        let titleLabel = UILabel()
        titleLabel.text = work.worksTitle ?? "无标题"
        titleLabel.font = .systemFont(ofSize: 16, weight: .medium)
        titleLabel.textColor = UIColor(hex: "#333333")
        titleLabel.numberOfLines = 2
        containerView.addSubview(titleLabel)

        // 分类和统计信息
        let infoLabel = UILabel()
        var infoText = ""
        // VideoItem没有worksCategoryName属性，可以根据worksType显示类型
        if let worksType = work.worksType {
            if worksType == 2 {
                infoText += "#笔记"
            } else {
                infoText += "#视频"
            }
        }
        infoLabel.text = infoText
        infoLabel.font = .systemFont(ofSize: 12)
        infoLabel.textColor = UIColor(hex: "#FF6236")
        containerView.addSubview(infoLabel)

        // 统计信息容器
        let statsContainerView = UIView()
        containerView.addSubview(statsContainerView)

        // 点赞数（移动到原观看数位置）
        let likesImageView = UIImageView()
        likesImageView.image = UIImage(named: "likes_icon")
        likesImageView.contentMode = .scaleAspectFit
        statsContainerView.addSubview(likesImageView)

        let likesLabel = UILabel()
        let likeCount = work.likeNumber ?? 0
        likesLabel.text = "\(likeCount)"
        likesLabel.font = .systemFont(ofSize: 12)
        likesLabel.textColor = UIColor(hex: "#999999")
        statsContainerView.addSubview(likesLabel)

        // 布局
        selectButton.translatesAutoresizingMaskIntoConstraints = false
        coverImageView.translatesAutoresizingMaskIntoConstraints = false
        durationLabel.translatesAutoresizingMaskIntoConstraints = false
        titleLabel.translatesAutoresizingMaskIntoConstraints = false
        infoLabel.translatesAutoresizingMaskIntoConstraints = false
        statsContainerView.translatesAutoresizingMaskIntoConstraints = false
        likesImageView.translatesAutoresizingMaskIntoConstraints = false
        likesLabel.translatesAutoresizingMaskIntoConstraints = false

        NSLayoutConstraint.activate([
            selectButton.leftAnchor.constraint(equalTo: containerView.leftAnchor, constant: 12),
            selectButton.centerYAnchor.constraint(equalTo: containerView.centerYAnchor),
            selectButton.widthAnchor.constraint(equalToConstant: 20),
            selectButton.heightAnchor.constraint(equalToConstant: 20),

            coverImageView.leftAnchor.constraint(equalTo: containerView.leftAnchor, constant: 36),
            coverImageView.topAnchor.constraint(equalTo: containerView.topAnchor, constant: 16),
            coverImageView.bottomAnchor.constraint(equalTo: containerView.bottomAnchor, constant: -16),
            coverImageView.widthAnchor.constraint(equalToConstant: 100),
            coverImageView.heightAnchor.constraint(equalToConstant: 166),

            titleLabel.leftAnchor.constraint(equalTo: coverImageView.rightAnchor, constant: 12),
            titleLabel.topAnchor.constraint(equalTo: containerView.topAnchor, constant: 16),
            titleLabel.rightAnchor.constraint(equalTo: containerView.rightAnchor, constant: -12),

            infoLabel.leftAnchor.constraint(equalTo: titleLabel.leftAnchor),
            infoLabel.topAnchor.constraint(equalTo: titleLabel.bottomAnchor, constant: 8),
            infoLabel.rightAnchor.constraint(equalTo: titleLabel.rightAnchor),

            statsContainerView.leftAnchor.constraint(equalTo: titleLabel.leftAnchor),
            statsContainerView.topAnchor.constraint(equalTo: infoLabel.bottomAnchor, constant: 8),
            statsContainerView.rightAnchor.constraint(equalTo: titleLabel.rightAnchor),
            statsContainerView.heightAnchor.constraint(equalToConstant: 16),

            // 统计信息内部布局
            likesImageView.leftAnchor.constraint(equalTo: statsContainerView.leftAnchor),
            likesImageView.centerYAnchor.constraint(equalTo: statsContainerView.centerYAnchor),
            likesImageView.widthAnchor.constraint(equalToConstant: 16),
            likesImageView.heightAnchor.constraint(equalToConstant: 16),

            likesLabel.leftAnchor.constraint(equalTo: likesImageView.rightAnchor, constant: 4),
            likesLabel.centerYAnchor.constraint(equalTo: statsContainerView.centerYAnchor)
        ])

        // 时长标签约束（仅当有时长时）
        if let duration = work.duration, duration > 0 {
            NSLayoutConstraint.activate([
                durationLabel.rightAnchor.constraint(equalTo: coverImageView.rightAnchor, constant: -4),
                durationLabel.bottomAnchor.constraint(equalTo: coverImageView.bottomAnchor, constant: -4),
                durationLabel.widthAnchor.constraint(greaterThanOrEqualToConstant: 40),
                durationLabel.heightAnchor.constraint(equalToConstant: 20)
            ])
        }

        return containerView
    }

    // MARK: - 选项点击事件
    @objc private func reportContentOptionTapped(_ sender: UIButton) {
        let index = sender.tag - 1000

        // 多选逻辑
        if selectedContentIndices.contains(index) {
            // 取消选择
            selectedContentIndices.remove(index)
        } else {
            // 添加选择
            selectedContentIndices.insert(index)
        }

        // 更新UI
        updateContentOptionsUI()
        updateSubmitButtonState()
    }

    @objc private func reportReasonOptionTapped(_ sender: UIButton) {
        let index = sender.tag - 2000

        // 单选逻辑
        selectedReasonIndex = index

        // 更新UI
        updateReasonOptionsUI()
        updateSubmitButtonState()
    }

    private func updateContentOptionsUI() {
        for (index, _) in reportContentData.enumerated() {
            if let button = reportContentContainerView.viewWithTag(1000 + index) as? UIButton,
               let radioButton = button.subviews.first(where: { $0 is UIButton }) as? UIButton {
                radioButton.isSelected = selectedContentIndices.contains(index)
            }
        }
    }

    private func updateReasonOptionsUI() {
        for (index, _) in reportReasonData.enumerated() {
            if let button = reportReasonContainerView.viewWithTag(2000 + index) as? UIButton,
               let radioButton = button.subviews.first(where: { $0 is UIButton }) as? UIButton {
                radioButton.isSelected = (selectedReasonIndex == index)
            }
        }
    }

    @objc private func workItemSelectTapped(_ sender: UIButton) {
        let index = sender.tag

        // 多选逻辑
        if selectedWorksIndices.contains(index) {
            selectedWorksIndices.remove(index)
        } else {
            selectedWorksIndices.insert(index)
        }

        // 更新UI
        updateWorksSelectionUI()
        updateSubmitButtonState()
    }

    private func updateWorksSelectionUI() {
        for (index, _) in worksListData.enumerated() {
            if let workItemView = worksListContainerView.viewWithTag(3000 + index) {
                // 找到选择按钮（第一个UIButton且tag为index）
                for subview in workItemView.subviews {
                    if let selectButton = subview as? UIButton, selectButton.tag == index {
                        selectButton.isSelected = selectedWorksIndices.contains(index)
                        break
                    }
                }
            }
        }
    }
    
    // MARK: - 公开方法（用于与视频举报页同步）
    
    /// 从外部移除指定视频的选中状态（用于与视频举报页同步）
    /// - Parameter videoItem: 要移除选中状态的视频项
    func removeVideoSelection(videoItem: VideoItem) {
        // 找到视频在列表中的索引
        if let index = worksListData.firstIndex(where: { $0.id == videoItem.id }) {
            // 从选中集合中移除
            selectedWorksIndices.remove(index)
            
            // 更新UI
            updateWorksSelectionUI()
            
            // 更新提交按钮状态
            updateSubmitButtonState()
        }
    }
    
    /// 获取当前选中的视频数量
    var selectedVideosCount: Int {
        return selectedWorksIndices.count
    }

    // MARK: - 提交举报
    private func submitUserInfoReport() {
        guard !selectedContentIndices.isEmpty,
              let reasonIndex = selectedReasonIndex,
              reasonIndex < reportReasonData.count else {
            return
        }

        let selectedContents = selectedContentIndices.compactMap { index in
            index < reportContentData.count ? reportContentData[index] : nil
        }
        let selectedReason = reportReasonData[reasonIndex]

        print("提交个人信息举报:")
        print("用户ID: \(userId)")
        print("举报内容: \(selectedContents.map { "\($0.label) (\($0.value))" }.joined(separator: ", "))")
        print("举报理由: \(selectedReason.label) (\(selectedReason.value))")

        // 使用举报类型管理器构建请求数据
        var builder = ReportTypeManager.PersonalInfoReportBuilder()
        builder.reportedUserId = userId
        builder.reportedUserName = userName
        builder.reportedUserAvatar = userAvatar
        builder.selectedContentValues = selectedContents.map { $0.value }
        builder.selectedReasonValue = selectedReason.value

        let reportRequest = builder.build()

        // 验证请求数据
        let validation = ReportTypeManager.shared.validateReportRequest(reportRequest)
        guard validation.isValid else {
            showErrorAlert(message: validation.errorMessage)
            return
        }

        // 打印调试信息
        ReportTypeManager.shared.printDebugInfo(for: reportRequest)

        // 显示加载状态
        showLoadingIndicator()

        // 调用举报提交API
        APIManager.shared.submitReport(reportData: reportRequest) { [weak self] result in
            DispatchQueue.main.async {
                self?.hideLoadingIndicator()

                switch result {
                case .success(let response):
                    if response.isSuccess {
                        self?.showSuccessAlert()
                    } else {
                        self?.showErrorAlert(message: response.displayMessage)
                    }
                case .failure(let error):
                    self?.showErrorAlert(message: error.errorMessage)
                }
            }
        }
    }

    private func submitWorksReport() {
        guard !selectedWorksIndices.isEmpty else { return }

        let selectedWorks = selectedWorksIndices.compactMap { index in
            index < worksListData.count ? worksListData[index] : nil
        }

        print("提交作品举报:")
        print("用户ID: \(userId)")
        print("选中的作品数量: \(selectedWorks.count)")
        for work in selectedWorks {
            print("作品ID: \(work.id ?? 0), 标题: \(work.worksTitle ?? "无标题")")
        }

        // TODO: 跳转到下一个页面或调用作品举报API
        // 这里可以跳转到作品举报详情页面
        showWorksReportNextStep(selectedWorks: selectedWorks)
    }

    private func showWorksReportNextStep(selectedWorks: [VideoItem]) {
        print("跳转到作品举报下一步页面，选中作品数量: \(selectedWorks.count)")

        // 跳转到视频举报页面
        let videoReportVC = VideoReportViewController(
            videoItems: selectedWorks,
            reportedUserId: userId,
            reportedUserName: userName,
            reportedUserAvatar: userAvatar
        )
        navigationController?.pushViewController(videoReportVC, animated: true)
    }

    private func showSuccessAlert() {
        let alert = UIAlertController(title: "举报成功", message: "感谢您的举报，我们会尽快处理", preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "确定", style: .default) { [weak self] _ in
            self?.navigationController?.popViewController(animated: true)
        })
        present(alert, animated: true)
    }

    // MARK: - Loading & Alert Methods

    private func showLoadingIndicator() {
        MBProgressHUD.showAdded(to: self.view, animated: true)
    }

    private func hideLoadingIndicator() {
        MBProgressHUD.hide(for: self.view, animated: true)
    }

    private func showErrorAlert(message: String) {
        let alert = UIAlertController(title: "举报失败", message: message, preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "确定", style: .default))
        present(alert, animated: true)
    }
}
